<?php
// includes/sidebar.php
$current_page = basename($_SERVER['PHP_SELF']);
?>
<div class="sidebar">
    <div class="sidebar-header">
        <h3><i class="fas fa-store"></i> K5N Apps</h3>
    </div>
    
    <ul class="sidebar-menu">
        <li>
            <a href="dashboard.php" class="<?php echo $current_page == 'dashboard.php' ? 'active' : ''; ?>">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        
        <?php if ($_SESSION['role'] == 'admin'): ?>
        <li>
            <a href="products.php" class="<?php echo $current_page == 'products.php' ? 'active' : ''; ?>">
                <i class="fas fa-box"></i> Data Barang
            </a>
        </li>
        <?php endif; ?>
        
        <li>
            <a href="sales.php" class="<?php echo $current_page == 'sales.php' ? 'active' : ''; ?>">
                <i class="fas fa-shopping-cart"></i> Penjualan
            </a>
        </li>
        
        <li>
            <a href="reports.php" class="<?php echo $current_page == 'reports.php' ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar"></i> Laporan
            </a>
        </li>
        
        <?php if ($_SESSION['role'] == 'admin'): ?>
        <li>
            <a href="users.php" class="<?php echo $current_page == 'users.php' ? 'active' : ''; ?>">
                <i class="fas fa-users"></i> Pengguna
            </a>
        </li>
        <?php endif; ?>
        
        <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
            <a href="logout.php">
                <i class="fas fa-sign-out-alt"></i> Logout
            </a>
        </li>
    </ul>
</div>