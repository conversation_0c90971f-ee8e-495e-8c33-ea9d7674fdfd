<?php
// print_receipt.php - Cetak Struk Penjualan
require_once 'config/database.php';
checkLogin();

$sale_id = (int)($_GET['id'] ?? 0);

if ($sale_id == 0) {
    die("Invalid sale ID");
}

$conn = connectDB();

// Get sale data
$sql = "SELECT s.*, u.full_name as cashier 
        FROM sales s 
        JOIN users u ON s.user_id = u.id 
        WHERE s.id = $sale_id";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    die("Sale not found");
}

$sale = $result->fetch_assoc();

// Get sale items
$sql = "SELECT si.*, p.name, p.code 
        FROM sale_items si 
        JOIN products p ON si.product_id = p.id 
        WHERE si.sale_id = $sale_id";
$items = $conn->query($sql);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk - <?php echo $sale['invoice_no']; ?></title>
    <style>
        @media print {
            body {
                margin: 0;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 10px;
            max-width: 280px;
        }
        
        .receipt {
            background: white;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .header p {
            margin: 2px 0;
            font-size: 11px;
        }
        
        .info {
            margin-bottom: 10px;
            font-size: 11px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .items {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 10px 0;
            margin-bottom: 10px;
        }
        
        .item {
            margin-bottom: 5px;
        }
        
        .item-name {
            font-weight: bold;
        }
        
        .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
        }
        
        .totals {
            margin-bottom: 10px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .total-row.grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px dashed #000;
            padding-top: 5px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 11px;
        }
        
        .footer p {
            margin: 2px 0;
        }
        
        @media screen {
            body {
                margin: 20px auto;
                border: 1px solid #ddd;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body onload="window.print()">
    <div class="receipt">
        <div class="header">
            <h1>K5N APPS</h1>
            <p>Sistem Inventory Management</p>
            <p>Jl. Contoh No. 123, Kota</p>
            <p>Telp: 021-1234567</p>
        </div>
        
        <div class="info">
            <div class="info-row">
                <span>No. Invoice:</span>
                <span><?php echo $sale['invoice_no']; ?></span>
            </div>
            <div class="info-row">
                <span>Tanggal:</span>
                <span><?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?></span>
            </div>
            <div class="info-row">
                <span>Kasir:</span>
                <span><?php echo $sale['cashier']; ?></span>
            </div>
        </div>
        
        <div class="items">
            <?php while ($item = $items->fetch_assoc()): ?>
            <div class="item">
                <div class="item-name"><?php echo $item['name']; ?></div>
                <div class="item-details">
                    <span><?php echo $item['quantity']; ?> x <?php echo formatRupiah($item['price']); ?></span>
                    <span><?php echo formatRupiah($item['subtotal']); ?></span>
                </div>
            </div>
            <?php endwhile; ?>
        </div>
        
        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span><?php echo formatRupiah($sale['total_amount']); ?></span>
            </div>
            <div class="total-row grand-total">
                <span>TOTAL:</span>
                <span><?php echo formatRupiah($sale['total_amount']); ?></span>
            </div>
            <div class="total-row">
                <span>Bayar (<?php echo ucfirst($sale['payment_method']); ?>):</span>
                <span><?php echo formatRupiah($sale['payment_received']); ?></span>
            </div>
            <div class="total-row">
                <span>Kembali:</span>
                <span><?php echo formatRupiah($sale['change_amount']); ?></span>
            </div>
        </div>
        
        <div class="footer">
            <p>================================</p>
            <p>Terima kasih atas kunjungan Anda</p>
            <p>Barang yang sudah dibeli</p>
            <p>tidak dapat dikembalikan</p>
            <p>================================</p>
        </div>
    </div>
    
    <script>
        // Auto close after print
        window.onafterprint = function() {
            window.close();
        }
    </script>
</body>
</html>
<?php $conn->close(); ?>