<?php
// ajax/process_sale.php - DEBUG VERSION
// Prevent any HTML output before JSON
ob_start();

// Clean any previous output
if (ob_get_level()) {
    ob_clean();
}

// Set JSON header first
header('Content-Type: application/json');

// Disable HTML error output
ini_set('display_errors', 0);
ini_set('log_errors', 1);

try {
    // Start session if not started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // Include database config
    require_once '../config/database.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not logged in. Please login first.');
    }
    
    // Get and validate input
    $input = file_get_contents('php://input');
    if (empty($input)) {
        throw new Exception('No input data received');
    }
    
    $data = json_decode($input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }
    
    // Validate required fields
    if (empty($data['items'])) {
        throw new Exception('No items in cart');
    }
    
    if (!isset($data['total']) || $data['total'] <= 0) {
        throw new Exception('Invalid total amount');
    }
    
    // Connect to database
    $conn = connectDB();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Extract and validate data
    $user_id = intval($_SESSION['user_id']);
    $items = $data['items'];
    $total = floatval($data['total']);
    $payment_method = isset($data['payment_method']) ? mysqli_real_escape_string($conn, $data['payment_method']) : 'cash';
    $payment_received = floatval($data['payment_received'] ?? 0);
    $change_amount = floatval($data['change_amount'] ?? 0);
    
    // Validate payment
    if ($payment_received < $total) {
        throw new Exception('Payment amount is insufficient');
    }
    
    // Start transaction
    mysqli_begin_transaction($conn);
    
    // Generate invoice number
    $invoice_no = generateInvoiceNumber($conn);
    
    // Insert sale record
    $stmt = mysqli_prepare($conn, "INSERT INTO sales (invoice_no, user_id, total_amount, payment_method, payment_received, change_amount) VALUES (?, ?, ?, ?, ?, ?)");
    
    if (!$stmt) {
        throw new Exception('Prepare statement failed: ' . mysqli_error($conn));
    }
    
    mysqli_stmt_bind_param($stmt, "sidsdd", $invoice_no, $user_id, $total, $payment_method, $payment_received, $change_amount);
    
    if (!mysqli_stmt_execute($stmt)) {
        throw new Exception('Failed to insert sale: ' . mysqli_stmt_error($stmt));
    }
    
    $sale_id = mysqli_insert_id($conn);
    mysqli_stmt_close($stmt);
    
    // Insert sale items
    $calculated_total = 0;
    foreach ($items as $item) {
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        $price = floatval($item['price']);
        $subtotal = $price * $quantity;
        $calculated_total += $subtotal;
        
        // Check stock
        $stock_check = mysqli_prepare($conn, "SELECT stock, name FROM products WHERE id = ?");
        mysqli_stmt_bind_param($stock_check, "i", $product_id);
        mysqli_stmt_execute($stock_check);
        $result = mysqli_stmt_get_result($stock_check);
        $product = mysqli_fetch_assoc($result);
        
        if (!$product) {
            throw new Exception('Product not found: ID ' . $product_id);
        }
        
        if ($product['stock'] < $quantity) {
            throw new Exception('Insufficient stock for ' . $product['name']);
        }
        
        // Insert sale item
        $item_stmt = mysqli_prepare($conn, "INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) VALUES (?, ?, ?, ?, ?)");
        mysqli_stmt_bind_param($item_stmt, "iiidd", $sale_id, $product_id, $quantity, $price, $subtotal);
        
        if (!mysqli_stmt_execute($item_stmt)) {
            throw new Exception('Failed to insert sale item: ' . mysqli_stmt_error($item_stmt));
        }
        
        mysqli_stmt_close($item_stmt);
        mysqli_stmt_close($stock_check);
    }
    
    // Verify total
    if (abs($calculated_total - $total) > 0.01) {
        throw new Exception('Total mismatch detected');
    }
    
    // Commit transaction
    mysqli_commit($conn);
    
    // Success response
    $response = [
        'success' => true,
        'sale_id' => $sale_id,
        'invoice_no' => $invoice_no,
        'message' => 'Transaction completed successfully'
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback on error
    if (isset($conn)) {
        mysqli_rollback($conn);
    }
    
    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
} finally {
    // Clean up
    if (isset($conn)) {
        mysqli_close($conn);
    }
}

/**
 * Generate invoice number
 */
function generateInvoiceNumber($conn) {
    $today = date('Ymd');
    $result = mysqli_query($conn, "SELECT invoice_no FROM sales WHERE DATE(created_at) = CURDATE() ORDER BY id DESC LIMIT 1");
    
    if ($result && mysqli_num_rows($result) > 0) {
        $row = mysqli_fetch_assoc($result);
        $last_number = intval(substr($row['invoice_no'], -4));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return 'INV' . $today . str_pad($new_number, 4, '0', STR_PAD_LEFT);
}
?>