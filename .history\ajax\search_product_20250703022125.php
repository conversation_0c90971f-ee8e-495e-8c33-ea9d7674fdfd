<?php
// ajax/search_product.php (VERSI AMAN)
require_once '../config/database.php';

header('Content-Type: application/json');

$barcode = $_GET['barcode'] ?? '';

if (!empty($barcode)) {
    $conn = connectDB();
    
    // Menggunakan prepared statement
    $stmt = $conn->prepare("SELECT * FROM products WHERE barcode = ? OR code = ? LIMIT 1");
    // Bind variabel $barcode ke kedua placeholder
    $stmt->bind_param("ss", $barcode, $barcode); // 's' untuk string
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $product = $result->fetch_assoc();
        echo json_encode(['success' => true, 'product' => $product]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Product not found']);
    }
    
    $stmt->close();
    $conn->close();
} else {
    echo json_encode(['success' => false, 'message' => 'No barcode provided']);
}
?>