<?php
// ajax/check_low_stock.php
require_once '../config/database.php';

header('Content-Type: application/json');

$conn = connectDB();

$result = $conn->query("SELECT COUNT(*) as count FROM products WHERE stock <= min_stock");
$count = $result->fetch_assoc()['count'];

$products = [];
if ($count > 0) {
    $result = $conn->query("SELECT code, name, stock, min_stock FROM products WHERE stock <= min_stock LIMIT 5");
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }
}

echo json_encode([
    'count' => $count,
    'products' => $products
]);

$conn->close();
?>

<?php
// ajax/search_products.php
require_once '../config/database.php';

header('Content-Type: application/json');

$query = $_GET['q'] ?? '';

if (strlen($query) < 2) {
    echo json_encode(['products' => []]);
    exit;
}

$conn = connectDB();
$query = escape($conn, $query);

$sql = "SELECT id, code, name, selling_price, stock 
        FROM products 
        WHERE (name LIKE '%$query%' OR code LIKE '%$query%') 
        AND stock > 0 
        LIMIT 10";

$result = $conn->query($sql);
$products = [];

while ($row = $result->fetch_assoc()) {
    $products[] = $row;
}

echo json_encode(['products' => $products]);

$conn->close();
?>

<?php
// includes/functions.php - Additional helper functions
require_once 'config/database.php';

// Generate unique filename
function generateUniqueFilename($extension) {
    return uniqid() . '_' . time() . '.' . $extension;
}

// Validate image upload
function validateImageUpload($file, $max_size = 2097152) { // 2MB default
    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
    $filename = $file['name'];
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if (!in_array($ext, $allowed)) {
        return ['success' => false, 'message' => 'Format file tidak diizinkan'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar'];
    }
    
    return ['success' => true, 'extension' => $ext];
}

// Get sales summary for dashboard
function getSalesSummary($conn, $period = 'today') {
    $where = "";
    
    switch($period) {
        case 'today':
            $where = "WHERE DATE(created_at) = CURDATE()";
            break;
        case 'week':
            $where = "WHERE YEARWEEK(created_at) = YEARWEEK(NOW())";
            break;
        case 'month':
            $where = "WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())";
            break;
        case 'year':
            $where = "WHERE YEAR(created_at) = YEAR(NOW())";
            break;
    }
    
    $sql = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total 
            FROM sales $where";
    
    $result = $conn->query($sql);
    return $result->fetch_assoc();
}

// Get best selling products
function getBestSellingProducts($conn, $limit = 5) {
    $sql = "SELECT p.*, SUM(si.quantity) as total_sold 
            FROM products p 
            JOIN sale_items si ON p.id = si.product_id 
            GROUP BY p.id 
            ORDER BY total_sold DESC 
            LIMIT $limit";
    
    return $conn->query($sql);
}

// Calculate profit margin
function calculateProfitMargin($purchase_price, $selling_price) {
    if ($selling_price == 0) return 0;
    return (($selling_price - $purchase_price) / $selling_price) * 100;
}

// Format date in Indonesian
function formatDateIndo($date) {
    $bulan = [
        1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    $split = explode('-', date('Y-m-d', strtotime($date)));
    return $split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}

// Check if product code exists
function isProductCodeExists($conn, $code, $exclude_id = null) {
    $code = escape($conn, $code);
    $sql = "SELECT id FROM products WHERE code = '$code'";
    
    if ($exclude_id) {
        $sql .= " AND id != $exclude_id";
    }
    
    $result = $conn->query($sql);
    return $result->num_rows > 0;
}

// Log user activity
function logUserActivity($conn, $user_id, $action, $description) {
    $action = escape($conn, $action);
    $description = escape($conn, $description);
    
    $sql = "INSERT INTO activity_logs (user_id, action, description) 
            VALUES ($user_id, '$action', '$description')";
    
    $conn->query($sql);
}

// Send low stock notification
function sendLowStockNotification($conn) {
    $sql = "SELECT * FROM products WHERE stock <= min_stock";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        // Here you could implement email notification or other alert system
        return $result->num_rows;
    }
    
    return 0;
}

// Clean old data
function cleanOldData($conn, $days = 365) {
    // Clean old activity logs
    $sql = "DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL $days DAY)";
    $conn->query($sql);
    
    // You can add more cleanup queries here
}

// Backup database
function backupDatabase($conn, $filename = null) {
    if (!$filename) {
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    }
    
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    $return = '';
    foreach ($tables as $table) {
        $result = $conn->query("SELECT * FROM $table");
        $num_fields = $result->field_count;
        
        $return .= 'DROP TABLE IF EXISTS ' . $table . ';';
        $row2 = $conn->query("SHOW CREATE TABLE $table")->fetch_row();
        $return .= "\n\n" . $row2[1] . ";\n\n";
        
        while ($row = $result->fetch_row()) {
            $return .= 'INSERT INTO ' . $table . ' VALUES(';
            for ($j = 0; $j < $num_fields; $j++) {
                $row[$j] = addslashes($row[$j]);
                $row[$j] = str_replace("\n", "\\n", $row[$j]);
                if (isset($row[$j])) {
                    $return .= '"' . $row[$j] . '"';
                } else {
                    $return .= '""';
                }
                if ($j < ($num_fields - 1)) {
                    $return .= ',';
                }
            }
            $return .= ");\n";
        }
        $return .= "\n\n\n";
    }
    
    // Save file
    $handle = fopen('../backups/' . $filename, 'w+');
    fwrite($handle, $return);
    fclose($handle);
    
    return $filename;
}
?>