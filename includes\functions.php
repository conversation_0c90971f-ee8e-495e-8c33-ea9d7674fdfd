<?php
// includes/functions.php - Updated version dengan fungsi yang dip<PERSON><PERSON>an
require_once dirname(__DIR__) . '/config/database.php';

// ======================================
// FUNGSI YANG SUDAH ADA (EXISTING)
// ======================================

// Generate unique filename
function generateUniqueFilename($extension) {
    return uniqid() . '_' . time() . '.' . $extension;
}

// Validate image upload
function validateImageUpload($file, $max_size = 2097152) { // 2MB default
    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
    $filename = $file['name'];
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if (!in_array($ext, $allowed)) {
        return ['success' => false, 'message' => 'Format file tidak diizinkan'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar'];
    }
    
    return ['success' => true, 'extension' => $ext];
}

// Get sales summary for dashboard
function getSalesSummary($conn, $period = 'today') {
    $where = "";
    
    switch($period) {
        case 'today':
            $where = "WHERE DATE(created_at) = CURDATE()";
            break;
        case 'week':
            $where = "WHERE YEARWEEK(created_at) = YEARWEEK(NOW())";
            break;
        case 'month':
            $where = "WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())";
            break;
        case 'year':
            $where = "WHERE YEAR(created_at) = YEAR(NOW())";
            break;
    }
    
    $sql = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total 
            FROM sales $where";
    
    $result = $conn->query($sql);
    return $result->fetch_assoc();
}

// Get best selling products
function getBestSellingProducts($conn, $limit = 5) {
    $sql = "SELECT p.*, SUM(si.quantity) as total_sold 
            FROM products p 
            JOIN sale_items si ON p.id = si.product_id 
            GROUP BY p.id 
            ORDER BY total_sold DESC 
            LIMIT $limit";
    
    return $conn->query($sql);
}

// Calculate profit margin
function calculateProfitMargin($purchase_price, $selling_price) {
    if ($selling_price == 0) return 0;
    return (($selling_price - $purchase_price) / $selling_price) * 100;
}

// Format date in Indonesian
function formatDateIndo($date) {
    $bulan = [
        1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    $split = explode('-', date('Y-m-d', strtotime($date)));
    return $split[2] . ' ' . $bulan[(int)$split[1]] . ' ' . $split[0];
}

// Check if product code exists
function isProductCodeExists($conn, $code, $exclude_id = null) {
    $code = escape($conn, $code);
    $sql = "SELECT id FROM products WHERE code = '$code'";
    
    if ($exclude_id) {
        $sql .= " AND id != $exclude_id";
    }
    
    $result = $conn->query($sql);
    return $result->num_rows > 0;
}

// Log user activity (optional - if you want to add activity_logs table)
function logUserActivity($conn, $user_id, $action, $description) {
    // Uncomment if you add activity_logs table
    /*
    $action = escape($conn, $action);
    $description = escape($conn, $description);
    
    $sql = "INSERT INTO activity_logs (user_id, action, description) 
            VALUES ($user_id, '$action', '$description')";
    
    $conn->query($sql);
    */
}

// Send low stock notification
function sendLowStockNotification($conn) {
    $sql = "SELECT * FROM products WHERE stock <= min_stock";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        // Here you could implement email notification or other alert system
        return $result->num_rows;
    }
    
    return 0;
}

// Clean old data
function cleanOldData($conn, $days = 365) {
    // Clean old sales data if needed
    // Be careful with this function!
    
    // Example: Delete sales older than X days
    // $sql = "DELETE FROM sales WHERE created_at < DATE_SUB(NOW(), INTERVAL $days DAY)";
    // $conn->query($sql);
}

// Backup database
function backupDatabase($conn, $filename = null) {
    if (!$filename) {
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    }
    
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_row()) {
        $tables[] = $row[0];
    }
    
    $return = '';
    foreach ($tables as $table) {
        $result = $conn->query("SELECT * FROM $table");
        $num_fields = $result->field_count;
        
        $return .= 'DROP TABLE IF EXISTS ' . $table . ';';
        $row2 = $conn->query("SHOW CREATE TABLE $table")->fetch_row();
        $return .= "\n\n" . $row2[1] . ";\n\n";
        
        while ($row = $result->fetch_row()) {
            $return .= 'INSERT INTO ' . $table . ' VALUES(';
            for ($j = 0; $j < $num_fields; $j++) {
                $row[$j] = addslashes($row[$j]);
                $row[$j] = str_replace("\n", "\\n", $row[$j]);
                if (isset($row[$j])) {
                    $return .= '"' . $row[$j] . '"';
                } else {
                    $return .= '""';
                }
                if ($j < ($num_fields - 1)) {
                    $return .= ',';
                }
            }
            $return .= ");\n";
        }
        $return .= "\n\n\n";
    }
    
    // Save file
    $backup_dir = dirname(__DIR__) . '/backups/';
    if (!file_exists($backup_dir)) {
        mkdir($backup_dir, 0777, true);
    }
    
    $handle = fopen($backup_dir . $filename, 'w+');
    fwrite($handle, $return);
    fclose($handle);
    
    return $filename;
}

// Format bytes to human readable
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Generate random string
if (!function_exists('generateRandomString')) {
    function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }
}

// Validate email
if (!function_exists('validateEmail')) {
    function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
}

// Sanitize input
if (!function_exists('sanitizeInput')) {
    function sanitizeInput($input) {
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input);
        return $input;
    }
}

// ======================================
// FUNGSI TAMBAHAN YANG DIPERLUKAN
// ======================================

/**
 * Generate invoice number (DIPERLUKAN untuk process_sale.php)
 */
if (!function_exists('generateInvoiceNo')) {
    function generateInvoiceNo($conn) {
        $today = date('Ymd');
        
        // Get last invoice number for today
        $result = $conn->query("SELECT invoice_no FROM sales WHERE DATE(created_at) = CURDATE() ORDER BY id DESC LIMIT 1");
        
        if ($result && $result->num_rows > 0) {
            $last_invoice = $result->fetch_assoc()['invoice_no'];
            $last_number = intval(substr($last_invoice, -4));
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return 'INV' . $today . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }
}

/**
 * Escape string untuk SQL (jika belum ada di database.php)
 */
if (!function_exists('escape')) {
    function escape($conn, $string) {
        return mysqli_real_escape_string($conn, trim($string));
    }
}

/**
 * Format rupiah (jika belum ada di database.php)
 */
if (!function_exists('formatRupiah')) {
    function formatRupiah($amount) {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}

/**
 * Generate product code
 */
if (!function_exists('generateProductCode')) {
    function generateProductCode($conn, $category_name) {
        // Get first 3 letters of category name
        $prefix = strtoupper(substr($category_name, 0, 3));
        
        // Get last product code for this category
        $result = $conn->query("SELECT code FROM products WHERE code LIKE '$prefix%' ORDER BY code DESC LIMIT 1");
        
        if ($result && $result->num_rows > 0) {
            $last_code = $result->fetch_assoc()['code'];
            $last_number = intval(substr($last_code, 3));
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . str_pad($new_number, 5, '0', STR_PAD_LEFT);
    }
}

/**
 * Check login status
 */
if (!function_exists('checkLogin')) {
    function checkLogin() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id'])) {
            header('Location: login.php');
            exit();
        }
    }
}

/**
 * Check admin status
 */
if (!function_exists('checkAdmin')) {
    function checkAdmin() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
            header('Location: login.php');
            exit();
        }
    }
}

/**
 * Get low stock products
 */
function getLowStockProducts($conn, $limit = 10) {
    $query = "SELECT * FROM products WHERE stock <= min_stock ORDER BY stock ASC LIMIT ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    return $stmt->get_result();
}

/**
 * Calculate profit
 */
function calculateProfit($selling_price, $purchase_price, $quantity = 1) {
    return ($selling_price - $purchase_price) * $quantity;
}

/**
 * Get sales report data
 */
function getSalesReport($conn, $start_date, $end_date) {
    $query = "
        SELECT 
            DATE(s.created_at) as sale_date,
            COUNT(s.id) as total_transactions,
            SUM(s.total_amount) as total_sales,
            SUM(si.quantity) as total_items
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE DATE(s.created_at) BETWEEN ? AND ?
        GROUP BY DATE(s.created_at)
        ORDER BY sale_date ASC
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    return $stmt->get_result();
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
?>