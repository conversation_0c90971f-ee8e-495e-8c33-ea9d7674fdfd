// assets/js/main.js - K5N Apps JavaScript Utilities

// Format number to Rupiah
function formatRupiah(angka) {
    return 'Rp ' + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Parse Rupiah string to number
function parseRupiah(rupiah) {
    return parseInt(rupiah.replace(/[^0-9]/g, ''));
}

// Show loading spinner
function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'spinner';
    loading.id = 'loadingSpinner';
    document.body.appendChild(loading);
}

// Hide loading spinner
function hideLoading() {
    const loading = document.getElementById('loadingSpinner');
    if (loading) {
        loading.remove();
    }
}

// Show alert message
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    // Insert at the beginning of content
    const content = document.querySelector('.content');
    content.insertBefore(alertDiv, content.firstChild);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Confirm dialog
function confirmAction(message) {
    return confirm(message);
}

// AJAX helper function
async function ajaxRequest(url, options = {}) {
    try {
        showLoading();
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        
        hideLoading();
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        return await response.json();
    } catch (error) {
        hideLoading();
        console.error('Ajax error:', error);
        showAlert('Terjadi kesalahan. Silakan coba lagi.', 'danger');
        return null;
    }
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Product search with debounce
const searchProducts = debounce(function(keyword) {
    if (keyword.length < 2) return;
    
    ajaxRequest(`ajax/search_products.php?q=${keyword}`)
        .then(data => {
            if (data && data.products) {
                displaySearchResults(data.products);
            }
        });
}, 300);

// Display search results
function displaySearchResults(products) {
    const resultsDiv = document.getElementById('searchResults');
    if (!resultsDiv) return;
    
    let html = '';
    products.forEach(product => {
        html += `
            <div class="search-result-item" onclick="selectProduct(${product.id})">
                <strong>${product.name}</strong>
                <span>${product.code} - ${formatRupiah(product.selling_price)}</span>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
    resultsDiv.style.display = products.length > 0 ? 'block' : 'none';
}

// Print function with custom settings
function printElement(elementId) {
    const printContent = document.getElementById(elementId).innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;
    
    // Reload page to restore event listeners
    location.reload();
}

// Date range picker helper
function setDateRange(range) {
    const today = new Date();
    let startDate, endDate;
    
    switch(range) {
        case 'today':
            startDate = endDate = today;
            break;
        case 'yesterday':
            startDate = endDate = new Date(today.setDate(today.getDate() - 1));
            break;
        case 'week':
            startDate = new Date(today.setDate(today.getDate() - 7));
            endDate = new Date();
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
    }
    
    document.getElementById('start_date').value = formatDate(startDate);
    document.getElementById('end_date').value = formatDate(endDate);
}

// Format date for input
function formatDate(date) {
    const d = new Date(date);
    const month = ('0' + (d.getMonth() + 1)).slice(-2);
    const day = ('0' + d.getDate()).slice(-2);
    return d.getFullYear() + '-' + month + '-' + day;
}

// Auto logout after inactivity
let inactivityTimer;
const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes

function resetInactivityTimer() {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(() => {
        if (confirm('Sesi Anda akan berakhir karena tidak ada aktivitas. Lanjutkan?')) {
            resetInactivityTimer();
        } else {
            window.location.href = 'logout.php';
        }
    }, INACTIVITY_TIMEOUT);
}

// Monitor user activity
document.addEventListener('DOMContentLoaded', function() {
    // Reset timer on user activity
    ['mousedown', 'keypress', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, resetInactivityTimer, true);
    });
    
    // Start timer
    resetInactivityTimer();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize data tables
    initializeDataTables();
});

// Initialize tooltips
function initializeTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const text = e.target.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
    tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
}

function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Initialize sortable data tables
function initializeDataTables() {
    const tables = document.querySelectorAll('.sortable-table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, index));
        });
    });
}

// Sort table function
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isNumeric = rows.length > 0 && !isNaN(rows[0].cells[column].textContent);
    
    rows.sort((a, b) => {
        const aValue = a.cells[column].textContent;
        const bValue = b.cells[column].textContent;
        
        if (isNumeric) {
            return parseFloat(aValue) - parseFloat(bValue);
        }
        return aValue.localeCompare(bValue);
    });
    
    // Toggle sort direction
    if (table.dataset.sortColumn == column && table.dataset.sortDirection == 'asc') {
        rows.reverse();
        table.dataset.sortDirection = 'desc';
    } else {
        table.dataset.sortDirection = 'asc';
    }
    
    table.dataset.sortColumn = column;
    
    // Re-append rows
    rows.forEach(row => tbody.appendChild(row));
}

// Export table to CSV
function exportTableToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    let csv = [];
    
    // Get headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv.push(headers.join(','));
    
    // Get rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach(td => {
            let text = td.textContent.trim();
            // Escape quotes and wrap in quotes if contains comma
            if (text.includes(',') || text.includes('"')) {
                text = '"' + text.replace(/"/g, '""') + '"';
            }
            row.push(text);
        });
        csv.push(row.join(','));
    });
    
    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename || 'export.csv';
    link.click();
}

// Barcode scanner handler
class BarcodeScanner {
    constructor(inputElement, callback) {
        this.input = inputElement;
        this.callback = callback;
        this.buffer = '';
        this.timeout = null;
        
        this.init();
    }
    
    init() {
        document.addEventListener('keydown', (e) => {
            // If input is focused, let normal typing work
            if (document.activeElement === this.input) {
                return;
            }
            
            // Clear buffer after 100ms of inactivity
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.buffer = '';
            }, 100);
            
            // Add to buffer
            if (e.key.length === 1) {
                this.buffer += e.key;
            } else if (e.key === 'Enter' && this.buffer.length > 0) {
                this.processBarcode(this.buffer);
                this.buffer = '';
            }
        });
    }
    
    processBarcode(barcode) {
        this.input.value = barcode;
        this.input.focus();
        if (this.callback) {
            this.callback(barcode);
        }
    }
}

// Quick calculator for POS
function showCalculator() {
    const calc = document.createElement('div');
    calc.className = 'calculator-popup';
    calc.innerHTML = `
        <div class="calculator">
            <div class="calc-display">0</div>
            <div class="calc-buttons">
                <button onclick="calcClear()">C</button>
                <button onclick="calcInput('/')">/</button>
                <button onclick="calcInput('*')">*</button>
                <button onclick="calcDelete()">←</button>
                
                <button onclick="calcInput('7')">7</button>
                <button onclick="calcInput('8')">8</button>
                <button onclick="calcInput('9')">9</button>
                <button onclick="calcInput('-')">-</button>
                
                <button onclick="calcInput('4')">4</button>
                <button onclick="calcInput('5')">5</button>
                <button onclick="calcInput('6')">6</button>
                <button onclick="calcInput('+')">+</button>
                
                <button onclick="calcInput('1')">1</button>
                <button onclick="calcInput('2')">2</button>
                <button onclick="calcInput('3')">3</button>
                <button onclick="calcEquals()" class="equals">=</button>
                
                <button onclick="calcInput('0')" class="zero">0</button>
                <button onclick="calcInput('.')">.</button>
            </div>
            <button onclick="closeCalculator()" class="calc-close">Close</button>
        </div>
    `;
    
    document.body.appendChild(calc);
}

let calcDisplay = '0';
let calcNewNumber = true;

function calcInput(value) {
    const display = document.querySelector('.calc-display');
    if (calcNewNumber && !isNaN(value)) {
        calcDisplay = value;
        calcNewNumber = false;
    } else {
        calcDisplay += value;
    }
    display.textContent = calcDisplay;
}

function calcClear() {
    calcDisplay = '0';
    calcNewNumber = true;
    document.querySelector('.calc-display').textContent = calcDisplay;
}

function calcDelete() {
    calcDisplay = calcDisplay.slice(0, -1) || '0';
    document.querySelector('.calc-display').textContent = calcDisplay;
}

function calcEquals() {
    try {
        calcDisplay = eval(calcDisplay).toString();
        calcNewNumber = true;
        document.querySelector('.calc-display').textContent = calcDisplay;
    } catch (e) {
        document.querySelector('.calc-display').textContent = 'Error';
        calcDisplay = '0';
        calcNewNumber = true;
    }
}

function closeCalculator() {
    document.querySelector('.calculator-popup').remove();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+P for new sale
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        window.location.href = 'sales.php';
    }
    
    // Ctrl+I for products
    if (e.ctrlKey && e.key === 'i') {
        e.preventDefault();
        window.location.href = 'products.php';
    }
    
    // Ctrl+R for reports
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        window.location.href = 'reports.php';
    }
    
    // ESC to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
});

// Notification system
class NotificationManager {
    static show(title, message, type = 'info') {
        if (!("Notification" in window)) {
            console.log("Browser doesn't support notifications");
            return;
        }
        
        if (Notification.permission === "granted") {
            new Notification(title, {
                body: message,
                icon: '/assets/img/logo.png'
            });
        } else if (Notification.permission !== "denied") {
            Notification.requestPermission().then(permission => {
                if (permission === "granted") {
                    new Notification(title, {
                        body: message,
                        icon: '/assets/img/logo.png'
                    });
                }
            });
        }
    }
    
    static checkLowStock() {
        ajaxRequest('ajax/check_low_stock.php')
            .then(data => {
                if (data && data.count > 0) {
                    this.show(
                        'Peringatan Stok',
                        `Ada ${data.count} produk dengan stok menipis!`,
                        'warning'
                    );
                }
            });
    }
}

// Check low stock every 5 minutes
setInterval(() => {
    NotificationManager.checkLowStock();
}, 5 * 60 * 1000);

// Initialize on page load
window.addEventListener('load', function() {
    // Request notification permission
    if ("Notification" in window && Notification.permission === "default") {
        Notification.requestPermission();
    }
});