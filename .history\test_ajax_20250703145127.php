<?php
// test_ajax.php - Test AJAX endpoints
session_start();

// Simulate logged in user for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

echo "<h2>K5N AJAX Endpoint Testing</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $conn = connectDB();
    echo "✅ Database connection: SUCCESS<br>";
    
    // Test query
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Users table accessible: $count users found<br>";
    } else {
        echo "❌ Error querying users table: " . $conn->error . "<br>";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "<br>";
}

// Test 2: Functions
echo "<h3>2. Functions Test</h3>";
try {
    $conn = connectDB();
    
    // Test formatRupiah
    $formatted = formatRupiah(35000);
    echo "✅ formatRupiah(35000): $formatted<br>";
    
    // Test generateInvoiceNo
    $invoice = generateInvoiceNo($conn);
    echo "✅ generateInvoiceNo(): $invoice<br>";
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Functions test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 3: Process Sale Endpoint (Simulation)
echo "<h3>3. Process Sale Test</h3>";
try {
    // Simulate POST data
    $testData = [
        'items' => [
            [
                'id' => 1,
                'name' => 'Test Product',
                'code' => 'TEST001',
                'price' => 35000,
                'quantity' => 1
            ]
        ],
        'total' => 35000,
        'payment_method' => 'cash',
        'payment_received' => 35000,
        'change_amount' => 0
    ];
    
    // Save original POST data
    $originalInput = file_get_contents('php://input');
    
    // Create temporary input
    $tempInput = json_encode($testData);
    
    echo "Test data prepared: " . json_encode($testData, JSON_PRETTY_PRINT) . "<br>";
    echo "✅ Process sale data structure: VALID<br>";
    
} catch (Exception $e) {
    echo "❌ Process sale test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 4: Check required tables
echo "<h3>4. Database Tables Check</h3>";
try {
    $conn = connectDB();
    
    $tables = ['users', 'products', 'categories', 'sales', 'sale_items', 'stock_history'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            // Count records
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "✅ Table '$table': EXISTS ($count records)<br>";
        } else {
            echo "❌ Table '$table': NOT FOUND<br>";
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Table check: FAILED - " . $e->getMessage() . "<br>";
}

// Test 5: Session Check
echo "<h3>5. Session Check</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'NOT SET') . "<br>";
echo "User Role: " . ($_SESSION['role'] ?? 'NOT SET') . "<br>";
echo "Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 'INACTIVE') . "<br>";

// Test 6: File Permissions
echo "<h3>6. File Permissions Check</h3>";
$files_to_check = [
    'config/database.php',
    'config/functions.php',
    'ajax/process_sale.php',
    'ajax/search_product.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "✅ File '$file': EXISTS (permissions: " . substr(sprintf('%o', $perms), -4) . ")<br>";
    } else {
        echo "❌ File '$file': NOT FOUND<br>";
    }
}

// Test 7: AJAX Endpoint Direct Test
echo "<h3>7. Direct AJAX Test</h3>";
echo "<button onclick='testAjaxEndpoint()'>Test Process Sale AJAX</button>";
echo "<div id='ajaxResult' style='margin-top: 10px; padding: 10px; background: #f0f0f0;'></div>";

?>

<script>
function testAjaxEndpoint() {
    const testData = {
        items: [
            {
                id: 1,
                name: 'Test Product',
                code: 'TEST001', 
                price: 35000,
                quantity: 1
            }
        ],
        total: 35000,
        payment_method: 'cash',
        payment_received: 35000,
        change_amount: 0
    };
    
    console.log('Testing AJAX endpoint with data:', testData);
    
    fetch('ajax/process_sale.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        document.getElementById('ajaxResult').innerHTML = '<pre>' + text + '</pre>';
        
        try {
            const data = JSON.parse(text);
            console.log('Parsed JSON:', data);
        } catch (e) {
            console.error('JSON parse error:', e);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('ajaxResult').innerHTML = 'Error: ' + error.message;
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; }
h3 { color: #666; margin-top: 20px; }
button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
button:hover { background: #005a8b; }
pre { background: #f5f5f5; padding: 10px; border-left: 3px solid #007cba; }
</style>