<?php
// system_status.php - Panduan lengkap dan status system
session_start();

// Set sebagai admin untuk testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

echo "<!DOCTYPE html>";
echo "<html lang='id'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>System Status - K5N Inventory</title>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1><i class='fas fa-cogs'></i> K5N Inventory System Status</h1>";

// Check system requirements
echo "<div class='section'>";
echo "<h2><i class='fas fa-check-circle'></i> System Requirements Check</h2>";

$checks = [];

// PHP Version
$php_version = phpversion();
$checks[] = [
    'name' => 'PHP Version',
    'status' => version_compare($php_version, '7.4', '>='),
    'message' => "Current: $php_version (Required: 7.4+)",
    'icon' => 'fa-php'
];

// MySQLi Extension
$checks[] = [
    'name' => 'MySQLi Extension',
    'status' => extension_loaded('mysqli'),
    'message' => extension_loaded('mysqli') ? 'Available' : 'Not installed',
    'icon' => 'fa-database'
];

// Session Support
$checks[] = [
    'name' => 'Session Support',
    'status' => function_exists('session_start'),
    'message' => function_exists('session_start') ? 'Available' : 'Not available',
    'icon' => 'fa-user-check'
];

// JSON Support
$checks[] = [
    'name' => 'JSON Support',
    'status' => function_exists('json_encode'),
    'message' => function_exists('json_encode') ? 'Available' : 'Not available',
    'icon' => 'fa-code'
];

foreach ($checks as $check) {
    $status_class = $check['status'] ? 'success' : 'error';
    $icon_class = $check['status'] ? 'fa-check' : 'fa-times';
    echo "<div class='check-item $status_class'>";
    echo "<i class='fas {$check['icon']}'></i>";
    echo "<span class='check-name'>{$check['name']}</span>";
    echo "<span class='check-status'><i class='fas $icon_class'></i> {$check['message']}</span>";
    echo "</div>";
}
echo "</div>";

// Database Connection Test
echo "<div class='section'>";
echo "<h2><i class='fas fa-database'></i> Database Connection</h2>";

try {
    require_once 'config/database.php';
    $conn = connectDB();
    
    echo "<div class='check-item success'>";
    echo "<i class='fas fa-database'></i>";
    echo "<span class='check-name'>Database Connection</span>";
    echo "<span class='check-status'><i class='fas fa-check'></i> Connected successfully</span>";
    echo "</div>";
    
    // Test database configuration
    $db_info = $conn->query("SELECT DATABASE() as db_name, VERSION() as version")->fetch_assoc();
    echo "<div class='info-box'>";
    echo "<strong>Database:</strong> {$db_info['db_name']}<br>";
    echo "<strong>MySQL Version:</strong> {$db_info['version']}<br>";
    echo "<strong>Connection:</strong> " . DB_HOST . " (User: " . DB_USER . ")";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='check-item error'>";
    echo "<i class='fas fa-database'></i>";
    echo "<span class='check-name'>Database Connection</span>";
    echo "<span class='check-status'><i class='fas fa-times'></i> Failed: {$e->getMessage()}</span>";
    echo "</div>";
    
    echo "<div class='error-box'>";
    echo "<h4>Database Connection Failed!</h4>";
    echo "<p>Please check your database configuration in <code>config/database.php</code>:</p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/MySQL is running</li>";
    echo "<li>Verify database name: <strong>" . DB_NAME . "</strong></li>";
    echo "<li>Check username and password</li>";
    echo "<li>Ensure database exists</li>";
    echo "</ul>";
    echo "</div>";
    $conn = null;
}

// File Structure Check
echo "<div class='section'>";
echo "<h2><i class='fas fa-folder-tree'></i> File Structure</h2>";

$required_files = [
    'config/database.php' => 'Database configuration',
    'includes/functions.php' => 'Helper functions',
    'ajax/process_sale.php' => 'Sales processing endpoint',
    'ajax/search_product.php' => 'Product search endpoint',
    'ajax/get_product.php' => 'Product data endpoint',
    'sales.php' => 'Sales interface',
    'products.php' => 'Product management',
    'print_receipt.php' => 'Receipt printing'
];

foreach ($required_files as $file => $description) {
    $exists = file_exists($file);
    $status_class = $exists ? 'success' : 'error';
    $icon_class = $exists ? 'fa-check' : 'fa-times';
    
    echo "<div class='check-item $status_class'>";
    echo "<i class='fas fa-file-code'></i>";
    echo "<span class='check-name'>$file</span>";
    echo "<span class='check-status'><i class='fas $icon_class'></i> $description</span>";
    echo "</div>";
}
echo "</div>";

// Database Tables Check
if ($conn) {
    echo "<div class='section'>";
    echo "<h2><i class='fas fa-table'></i> Database Tables</h2>";
    
    $required_tables = [
        'users' => 'User accounts',
        'categories' => 'Product categories',
        'products' => 'Product inventory',
        'sales' => 'Sales transactions',
        'sale_items' => 'Sales line items',
        'stock_history' => 'Stock movement history'
    ];
    
    $table_stats = [];
    
    foreach ($required_tables as $table => $description) {
        try {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            $exists = $result->num_rows > 0;
            
            if ($exists) {
                $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_result->fetch_assoc()['count'];
                $table_stats[$table] = $count;
                
                echo "<div class='check-item success'>";
                echo "<i class='fas fa-table'></i>";
                echo "<span class='check-name'>$table</span>";
                echo "<span class='check-status'><i class='fas fa-check'></i> $description ($count records)</span>";
                echo "</div>";
            } else {
                echo "<div class='check-item error'>";
                echo "<i class='fas fa-table'></i>";
                echo "<span class='check-name'>$table</span>";
                echo "<span class='check-status'><i class='fas fa-times'></i> Table not found</span>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='check-item error'>";
            echo "<i class='fas fa-table'></i>";
            echo "<span class='check-name'>$table</span>";
            echo "<span class='check-status'><i class='fas fa-times'></i> Error: {$e->getMessage()}</span>";
            echo "</div>";
        }
    }
    
    // Data summary
    if (count($table_stats) > 0) {
        echo "<div class='info-box'>";
        echo "<h4>Data Summary:</h4>";
        $products_with_stock = 0;
        if (isset($table_stats['products']) && $table_stats['products'] > 0) {
            $stock_result = $conn->query("SELECT COUNT(*) as count FROM products WHERE stock > 0");
            $products_with_stock = $stock_result->fetch_assoc()['count'];
        }
        
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
        echo "<div><strong>Total Products:</strong> " . ($table_stats['products'] ?? 0) . "</div>";
        echo "<div><strong>Products in Stock:</strong> $products_with_stock</div>";
        echo "<div><strong>Total Sales:</strong> " . ($table_stats['sales'] ?? 0) . "</div>";
        echo "<div><strong>Total Users:</strong> " . ($table_stats['users'] ?? 0) . "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Function Tests
echo "<div class='section'>";
echo "<h2><i class='fas fa-cog'></i> Function Tests</h2>";

$functions_to_test = [
    'connectDB' => 'Database connection function',
    'escape' => 'SQL escaping function',
    'formatRupiah' => 'Currency formatting function',
    'generateInvoiceNo' => 'Invoice number generation',
    'checkLogin' => 'User authentication check',
    'getSalesSummary' => 'Sales summary function'
];

foreach ($functions_to_test as $function => $description) {
    $exists = function_exists($function);
    $status_class = $exists ? 'success' : 'error';
    $icon_class = $exists ? 'fa-check' : 'fa-times';
    
    echo "<div class='check-item $status_class'>";
    echo "<i class='fas fa-function'></i>";
    echo "<span class='check-name'>$function()</span>";
    echo "<span class='check-status'><i class='fas $icon_class'></i> $description</span>";
    echo "</div>";
}

// Test some functions
if (function_exists('formatRupiah')) {
    $test_format = formatRupiah(35000);
    echo "<div class='info-box'>";
    echo "<strong>Function Test Example:</strong> formatRupiah(35000) = $test_format";
    echo "</div>";
}

echo "</div>";

// Quick Actions
echo "<div class='section'>";
echo "<h2><i class='fas fa-tools'></i> Quick Actions</h2>";

echo "<div class='action-grid'>";

// Setup Sample Data
echo "<div class='action-card'>";
echo "<i class='fas fa-database'></i>";
echo "<h4>Setup Sample Data</h4>";
echo "<p>Add sample products and categories for testing</p>";
echo "<a href='setup_sample_data.php' class='btn btn-primary'>Setup Data</a>";
echo "</div>";

// Test System
echo "<div class='action-card'>";
echo "<i class='fas fa-vial'></i>";
echo "<h4>Test System</h4>";
echo "<p>Run comprehensive system tests</p>";
echo "<a href='test_existing_structure.php' class='btn btn-info'>Run Tests</a>";
echo "</div>";

// Open Sales
if ($conn && isset($table_stats['products']) && $table_stats['products'] > 0) {
    echo "<div class='action-card'>";
    echo "<i class='fas fa-cash-register'></i>";
    echo "<h4>Sales System</h4>";
    echo "<p>Open point of sales interface</p>";
    echo "<a href='sales.php' class='btn btn-success'>Open Sales</a>";
    echo "</div>";
}

// Manage Products
echo "<div class='action-card'>";
echo "<i class='fas fa-boxes'></i>";
echo "<h4>Manage Products</h4>";
echo "<p>Add, edit, and manage inventory</p>";
echo "<a href='products.php' class='btn btn-secondary'>Manage Products</a>";
echo "</div>";

echo "</div>";
echo "</div>";

// Troubleshooting Guide
echo "<div class='section'>";
echo "<h2><i class='fas fa-question-circle'></i> Troubleshooting Guide</h2>";

echo "<div class='troubleshoot-grid'>";

echo "<div class='trouble-card'>";
echo "<h4><i class='fas fa-exclamation-triangle'></i> Modal Stuck in Sales</h4>";
echo "<ul>";
echo "<li>Check browser console (F12) for JavaScript errors</li>";
echo "<li>Verify AJAX endpoints are working</li>";
echo "<li>Ensure products exist in database</li>";
echo "<li>Check user session is valid</li>";
echo "</ul>";
echo "</div>";

echo "<div class='trouble-card'>";
echo "<h4><i class='fas fa-times-circle'></i> Database Connection Failed</h4>";
echo "<ul>";
echo "<li>Start XAMPP Control Panel</li>";
echo "<li>Start Apache and MySQL services</li>";
echo "<li>Check database name in config/database.php</li>";
echo "<li>Verify MySQL is running on port 3306</li>";
echo "</ul>";
echo "</div>";

echo "<div class='trouble-card'>";
echo "<h4><i class='fas fa-file-times'></i> Function Not Found</h4>";
echo "<ul>";
echo "<li>Check if includes/functions.php exists</li>";
echo "<li>Verify config/database.php is properly included</li>";
echo "<li>Check for PHP syntax errors</li>";
echo "<li>Review file permissions</li>";
echo "</ul>";
echo "</div>";

echo "<div class='trouble-card'>";
echo "<h4><i class='fas fa-shopping-cart'></i> No Products Available</h4>";
echo "<ul>";
echo "<li>Run 'Setup Sample Data' to add test products</li>";
echo "<li>Check products table has records</li>";
echo "<li>Verify products have stock > 0</li>";
echo "<li>Add products via products.php</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>"; // End container

if ($conn) {
    $conn->close();
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f8f9fa;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

h1 {
    color: #007cba;
    border-bottom: 3px solid #007cba;
    padding-bottom: 15px;
    margin-bottom: 30px;
}

h2 {
    color: #495057;
    margin-top: 30px;
    margin-bottom: 20px;
    padding-left: 10px;
    border-left: 4px solid #007cba;
}

.section {
    margin-bottom: 40px;
}

.check-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    border-left: 4px solid;
}

.check-item.success {
    background-color: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.check-item.error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.check-item i:first-child {
    margin-right: 12px;
    width: 20px;
}

.check-name {
    flex: 1;
    font-weight: 600;
}

.check-status {
    font-size: 0.9em;
}

.check-status i {
    margin-right: 5px;
}

.info-box {
    background-color: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.error-box {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #721c24;
}

.error-box h4 {
    color: #721c24;
    margin-top: 0;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.action-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.action-card i {
    font-size: 2em;
    color: #007cba;
    margin-bottom: 10px;
}

.action-card h4 {
    color: #495057;
    margin: 10px 0;
}

.action-card p {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 15px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary { background-color: #007cba; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-info { background-color: #17a2b8; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.troubleshoot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.trouble-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
}

.trouble-card h4 {
    color: #856404;
    margin-top: 0;
    margin-bottom: 10px;
}

.trouble-card ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.trouble-card li {
    margin-bottom: 5px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', monospace;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .action-grid,
    .troubleshoot-grid {
        grid-template-columns: 1fr;
    }
}
</style>

</body>
</html>