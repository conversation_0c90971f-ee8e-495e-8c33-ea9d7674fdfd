<?php
// sales.php - Point of Sale (FIXED for Enhanced Database)
require_once 'config/database.php';
checkLogin();

$conn = connectDB();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penjualan - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .pos-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 1.5rem;
            height: calc(100vh - 180px);
        }
        
        .product-search {
            margin-bottom: 1rem;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            overflow-y: auto;
            padding: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
            max-height: calc(100vh - 300px);
        }
        
        .product-item {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }
        
        .product-item.low-stock {
            border-color: #f59e0b;
            background-color: #fffbeb;
        }
        
        .product-item.out-of-stock {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .stock-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ef4444;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
        }
        
        .stock-badge.low {
            background: #f59e0b;
        }
        
        .cart-container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
        }
        
        .cart-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            max-height: 400px;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .cart-footer {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background-color: var(--light-color);
        }
        
        .total-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            text-align: right;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background: white;
            border-radius: 0.5rem;
            border: 2px solid var(--primary-color);
        }
        
        .barcode-input {
            width: 100%;
            padding: 1rem;
            font-size: 1.2rem;
            border: 2px solid var(--primary-color);
            border-radius: 0.5rem;
            text-align: center;
            transition: all 0.3s;
        }
        
        .barcode-input:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
        
        .search-result-item {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .search-result-item:hover {
            background: var(--light-color);
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: white;
            border-radius: 0.25rem;
            padding: 0.25rem;
        }
        
        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 0.25rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .quantity-btn:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .customer-info {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
        }
        
        .payment-summary {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .payment-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .payment-row.total {
            font-weight: bold;
            font-size: 1.2rem;
            color: var(--primary-color);
            border-top: 1px solid var(--border-color);
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .pos-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .product-grid {
                max-height: 300px;
            }
            
            .cart-items {
                max-height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Penjualan</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Penjualan</span>
                    </div>
                </div>
                
                <div class="pos-container">
                    <!-- Product Selection -->
                    <div>
                        <div class="product-search" style="position: relative;">
                            <input type="text" 
                                   id="barcodeInput" 
                                   class="barcode-input" 
                                   placeholder="Scan barcode atau ketik nama/kode produk..."
                                   autocomplete="off"
                                   autofocus>
                            <div id="searchResults" class="search-results"></div>
                        </div>
                        
                        <div class="product-grid" id="productGrid">
                            <?php
                            // Use enhanced query with better data selection
                            $sql = "SELECT p.*, c.name as category_name 
                                   FROM products p 
                                   LEFT JOIN categories c ON p.category_id = c.id 
                                   WHERE p.is_active = TRUE AND p.stock > 0 
                                   ORDER BY p.name ASC 
                                   LIMIT 50";
                            
                            $products = $conn->query($sql);
                            
                            if ($products && $products->num_rows > 0):
                                while ($product = $products->fetch_assoc()):
                                    $stockClass = '';
                                    $stockBadge = '';
                                    
                                    if ($product['stock'] <= 0) {
                                        $stockClass = 'out-of-stock';
                                        $stockBadge = '<span class="stock-badge">Habis</span>';
                                    } elseif ($product['stock'] <= $product['min_stock']) {
                                        $stockClass = 'low-stock';
                                        $stockBadge = '<span class="stock-badge low">Sedikit</span>';
                                    }
                            ?>
                            <div class="product-item <?php echo $stockClass; ?>" 
                                 data-product='<?php echo htmlspecialchars(json_encode($product)); ?>'
                                 onclick="addToCart(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                <?php echo $stockBadge; ?>
                                <h4 title="<?php echo htmlspecialchars($product['name']); ?>">
                                    <?php echo strlen($product['name']) > 20 ? substr($product['name'], 0, 20) . '...' : $product['name']; ?>
                                </h4>
                                <p style="color: #6b7280; font-size: 0.875rem;">
                                    <?php echo $product['code']; ?>
                                </p>
                                <p style="font-weight: bold; color: var(--primary-color); margin: 0.5rem 0;">
                                    <?php echo formatRupiah($product['selling_price']); ?>
                                </p>
                                <p style="font-size: 0.75rem; color: #6b7280;">
                                    Stok: <?php echo $product['stock']; ?>
                                    <?php if (!empty($product['category_name'])): ?>
                                    <br><span style="background: #e5e7eb; padding: 2px 6px; border-radius: 10px;">
                                        <?php echo $product['category_name']; ?>
                                    </span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <?php 
                                endwhile;
                            else:
                            ?>
                            <div style="grid-column: 1/-1; text-align: center; padding: 2rem; color: #6b7280;">
                                <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                <p>Belum ada produk tersedia</p>
                                <p>Silakan tambah produk terlebih dahulu</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Cart -->
                    <div class="cart-container">
                        <div class="cart-header">
                            <h3><i class="fas fa-shopping-cart"></i> Keranjang</h3>
                            <button class="btn btn-sm" onclick="clearCart()" id="clearBtn" 
                                    style="background-color: #ef4444; color: white;">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                        
                        <div class="cart-items" id="cartItems">
                            <p style="text-align: center; color: #6b7280; padding: 2rem;">
                                <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                                Keranjang masih kosong
                            </p>
                        </div>
                        
                        <div class="cart-footer">
                            <div class="total-display">
                                Total: <span id="totalAmount">Rp 0</span>
                            </div>
                            
                            <button class="btn btn-success btn-block" onclick="processPayment()" id="payButton" disabled>
                                <i class="fas fa-cash-register"></i> Proses Pembayaran
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Modal -->
    <div id="paymentModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <h2><i class="fas fa-credit-card"></i> Proses Pembayaran</h2>
            
            <!-- Customer Info -->
            <div class="customer-info">
                <h4>Informasi Pelanggan (Opsional)</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Nama Pelanggan</label>
                        <input type="text" id="customerName" class="form-control" placeholder="Nama pelanggan">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Nomor HP</label>
                        <input type="tel" id="customerPhone" class="form-control" placeholder="08xxxxxxxxxx">
                    </div>
                </div>
            </div>
            
            <!-- Payment Summary -->
            <div class="payment-summary">
                <h4>Ringkasan Pembayaran</h4>
                <div class="payment-row">
                    <span>Subtotal:</span>
                    <span id="paymentSubtotal">Rp 0</span>
                </div>
                <div class="payment-row">
                    <span>Diskon:</span>
                    <span id="paymentDiscount">Rp 0</span>
                </div>
                <div class="payment-row total">
                    <span>TOTAL:</span>
                    <span id="paymentTotal">Rp 0</span>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div class="form-group">
                    <label class="form-label">Metode Pembayaran</label>
                    <select id="paymentMethod" class="form-control">
                        <option value="cash">Tunai</option>
                        <option value="card">Kartu Debit/Credit</option>
                        <option value="transfer">Transfer Bank</option>
                        <option value="ewallet">E-Wallet</option>
                        <option value="credit">Kredit</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Jumlah Bayar</label>
                    <input type="number" id="paymentAmount" class="form-control" 
                           style="font-size: 1.2rem;" oninput="calculateChange()" min="0">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Kembalian</label>
                <input type="text" id="changeAmount" class="form-control" readonly 
                       style="font-size: 1.2rem; font-weight: bold; color: var(--success-color); background: #f0f9ff;">
            </div>
            
            <div class="form-group">
                <label class="form-label">Catatan (Opsional)</label>
                <textarea id="saleNotes" class="form-control" rows="2" placeholder="Catatan tambahan..."></textarea>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 1.5rem;">
                <button type="button" class="btn" onclick="closePaymentModal()" 
                        style="background-color: #6b7280; color: white;" id="cancelBtn">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-success" onclick="completeSale()" id="completeBtn">
                    <i class="fas fa-check"></i> Selesaikan Transaksi
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let cart = [];
        let total = 0;
        let isProcessing = false;
        let searchTimeout;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateCart();
            document.getElementById('barcodeInput').focus();
            
            // Add event listeners
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Barcode input with improved handling
            const barcodeInput = document.getElementById('barcodeInput');
            
            barcodeInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    if (query) {
                        searchProduct(query);
                        this.value = '';
                        hideSearchResults();
                    }
                }
            });
            
            // Live search functionality
            barcodeInput.addEventListener('input', function(e) {
                const query = this.value.trim();
                
                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                
                if (query.length >= 2) {
                    // Delay search to avoid too many requests
                    searchTimeout = setTimeout(() => {
                        liveSearch(query);
                    }, 300);
                } else {
                    hideSearchResults();
                }
            });
            
            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.product-search')) {
                    hideSearchResults();
                }
            });
            
            // Payment method change handler
            document.getElementById('paymentMethod').addEventListener('change', function() {
                updatePaymentDisplay();
            });
        }
        
        // Live search function
        function liveSearch(query) {
            if (query.length < 2) return;
            
            fetch(`ajax/search_products.php?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.products && data.products.length > 0) {
                        displaySearchResults(data.products);
                    } else {
                        hideSearchResults();
                    }
                })
                .catch(error => {
                    console.error('Live search error:', error);
                    hideSearchResults();
                });
        }
        
        // Display search results
        function displaySearchResults(products) {
            const resultsDiv = document.getElementById('searchResults');
            let html = '';
            
            products.forEach(product => {
                const stockInfo = product.stock > 0 ? 
                    `<span style="color: #16a34a;">Stok: ${product.stock}</span>` : 
                    `<span style="color: #dc2626;">Stok Habis</span>`;
                
                html += `
                    <div class="search-result-item" onclick="selectFromSearch(${product.id})" data-product-id="${product.id}">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>${product.name}</strong><br>
                                <small>${product.code} - ${formatRupiah(product.selling_price)}</small>
                            </div>
                            <div style="text-align: right; font-size: 0.8rem;">
                                ${stockInfo}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }
        
        // Hide search results
        function hideSearchResults() {
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.style.display = 'none';
        }
        
        // Select product from search results
        function selectFromSearch(productId) {
            fetch(`ajax/get_product.php?id=${productId}`)
                .then(response => response.json())
                .then(data => {
                    if (data && !data.error) {
                        addToCart(data);
                        hideSearchResults();
                        document.getElementById('barcodeInput').value = '';
                        document.getElementById('barcodeInput').focus();
                    } else {
                        alert('Produk tidak ditemukan!');
                    }
                })
                .catch(error => {
                    console.error('Product fetch error:', error);
                    alert('Error mengambil data produk');
                });
        }
        
        // Search product by barcode/code
        function searchProduct(query) {
            console.log('Searching for:', query);
            
            // First try to find in current product grid
            const productItems = document.querySelectorAll('.product-item');
            let found = false;
            
            productItems.forEach(item => {
                try {
                    const productData = JSON.parse(item.getAttribute('data-product'));
                    if (productData.code === query || 
                        productData.barcode === query || 
                        productData.name.toLowerCase().includes(query.toLowerCase())) {
                        addToCart(productData);
                        found = true;
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing product data:', e);
                }
            });
            
            // If not found in grid, try AJAX search
            if (!found) {
                fetch('ajax/search_product.php?barcode=' + encodeURIComponent(query))
                    .then(response => response.json())
                    .then(data => {
                        console.log('AJAX Response:', data);
                        if (data.success && data.product) {
                            addToCart(data.product);
                        } else {
                            alert('Produk "' + query + '" tidak ditemukan!');
                        }
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        alert('Error mencari produk: ' + error.message);
                    });
            }
        }
        
        // Add product to cart with enhanced validation
        function addToCart(product) {
            console.log('Adding to cart:', product);
            
            // Validate product data
            if (!product || !product.id || !product.name || !product.selling_price) {
                alert('Data produk tidak valid!');
                return;
            }
            
            // Check if product is active and has stock
            if (product.stock <= 0) {
                alert('Produk ini sudah habis!');
                return;
            }
            
            const existingItem = cart.find(item => item.id == product.id);
            
            if (existingItem) {
                if (existingItem.quantity < product.stock) {
                    existingItem.quantity++;
                } else {
                    alert(`Stok tidak mencukupi!\nStok tersedia: ${product.stock}`);
                    return;
                }
            } else {
                cart.push({
                    id: parseInt(product.id),
                    code: product.code || 'N/A',
                    name: product.name,
                    price: parseFloat(product.selling_price),
                    stock: parseInt(product.stock),
                    quantity: 1
                });
            }
            
            updateCart();
            document.getElementById('barcodeInput').focus();
            
            // Show success feedback
            showToast(`${product.name} ditambahkan ke keranjang`, 'success');
        }
        
        // Remove item from cart
        function removeFromCart(index) {
            if (confirm('Hapus item ini dari keranjang?')) {
                const removedItem = cart[index];
                cart.splice(index, 1);
                updateCart();
                showToast(`${removedItem.name} dihapus dari keranjang`, 'info');
            }
        }
        
        // Update item quantity
        function updateQuantity(index, change) {
            const item = cart[index];
            const newQty = item.quantity + change;
            
            if (newQty > 0 && newQty <= item.stock) {
                item.quantity = newQty;
                updateCart();
            } else if (newQty <= 0) {
                removeFromCart(index);
            } else {
                alert(`Stok tidak mencukupi!\nStok tersedia: ${item.stock}`);
            }
        }
        
        // Clear entire cart
        function clearCart() {
            if (cart.length > 0 && confirm('Hapus semua item dari keranjang?')) {
                cart = [];
                updateCart();
                showToast('Keranjang dikosongkan', 'info');
            }
        }
        
        // Update cart display with enhanced UI
        function updateCart() {
            const cartItems = document.getElementById('cartItems');
            const payButton = document.getElementById('payButton');
            total = 0;
            
            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <p style="text-align: center; color: #6b7280; padding: 2rem;">
                        <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        Keranjang masih kosong
                    </p>
                `;
                payButton.disabled = true;
            } else {
                let html = '';
                cart.forEach((item, index) => {
                    const subtotal = item.price * item.quantity;
                    total += subtotal;
                    
                    html += `
                        <div class="cart-item">
                            <div style="flex: 1;">
                                <strong>${item.name}</strong><br>
                                <small style="color: #6b7280;">${item.code} - ${formatRupiah(item.price)}</small><br>
                                <small style="color: #16a34a;">Subtotal: ${formatRupiah(subtotal)}</small>
                            </div>
                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(${index}, -1)">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <span style="min-width: 30px; text-align: center; font-weight: bold;">${item.quantity}</span>
                                <button class="quantity-btn" onclick="updateQuantity(${index}, 1)">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="quantity-btn" onclick="removeFromCart(${index})" 
                                        style="background: #ef4444; color: white; margin-left: 0.5rem;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                cartItems.innerHTML = html;
                payButton.disabled = false;
            }
            
            document.getElementById('totalAmount').textContent = formatRupiah(total);
        }
        
        // Format currency
        function formatRupiah(amount) {
            return 'Rp ' + Math.round(amount).toLocaleString('id-ID');
        }
        
        // Process payment with enhanced UI
        function processPayment() {
            if (cart.length === 0 || isProcessing) return;
            
            // Update payment modal
            document.getElementById('paymentSubtotal').textContent = formatRupiah(total);
            document.getElementById('paymentDiscount').textContent = formatRupiah(0);
            document.getElementById('paymentTotal').textContent = formatRupiah(total);
            document.getElementById('paymentAmount').value = total;
            document.getElementById('changeAmount').value = formatRupiah(0);
            
            // Reset form
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';
            document.getElementById('saleNotes').value = '';
            document.getElementById('paymentMethod').value = 'cash';
            
            document.getElementById('paymentModal').style.display = 'block';
            
            // Focus on payment amount
            setTimeout(() => {
                document.getElementById('paymentAmount').focus();
                document.getElementById('paymentAmount').select();
            }, 100);
            
            // Reset processing state
            isProcessing = false;
            updateButtonStates();
        }
        
        // Close payment modal
        function closePaymentModal() {
            if (isProcessing) {
                console.log('Cannot close modal while processing');
                return;
            }
            document.getElementById('paymentModal').style.display = 'none';
        }
        
        // Calculate change
        function calculateChange() {
            const paid = parseFloat(document.getElementById('paymentAmount').value) || 0;
            const change = paid - total;
            document.getElementById('changeAmount').value = formatRupiah(Math.max(0, change));
        }
        
        // Update payment display
        function updatePaymentDisplay() {
            const method = document.getElementById('paymentMethod').value;
            const amountInput = document.getElementById('paymentAmount');
            
            // For non-cash payments, set exact amount
            if (method !== 'cash') {
                amountInput.value = total;
                calculateChange();
            }
        }
        
        // Update button states during processing
        function updateButtonStates() {
            const completeBtn = document.getElementById('completeBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            
            if (isProcessing) {
                completeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
                completeBtn.disabled = true;
                cancelBtn.disabled = true;
            } else {
                completeBtn.innerHTML = '<i class="fas fa-check"></i> Selesaikan Transaksi';
                completeBtn.disabled = false;
                cancelBtn.disabled = false;
            }
        }
        
        // Complete sale transaction with enhanced data
        function completeSale() {
            if (isProcessing) {
                console.log('Already processing, ignoring click');
                return;
            }
            
            const paymentMethod = document.getElementById('paymentMethod').value;
            const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
            const customerName = document.getElementById('customerName').value.trim();
            const customerPhone = document.getElementById('customerPhone').value.trim();
            const notes = document.getElementById('saleNotes').value.trim();
            
            // Validation
            if (paymentAmount < total) {
                alert('Jumlah pembayaran kurang dari total!');
                return;
            }
            
            if (cart.length === 0) {
                alert('Keranjang kosong!');
                return;
            }
            
            // Set processing state
            isProcessing = true;
            updateButtonStates();
            
            console.log('Starting transaction...');
            
            // Prepare enhanced sale data
            const saleData = {
                items: cart,
                subtotal: total,
                total_amount: total,
                payment_method: paymentMethod,
                payment_received: paymentAmount,
                change_amount: paymentAmount - total,
                customer_name: customerName,
                customer_phone: customerPhone,
                notes: notes
            };
            
            console.log('Sale data:', saleData);
            
            // Send to server
            fetch('ajax/process_sale.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(saleData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    
                    try {
                        const data = JSON.parse(text);
                        return { data, ok: response.ok };
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Server returned invalid JSON: ' + text.substring(0, 100));
                    }
                });
            })
            .then(({ data, ok }) => {
                console.log('Parsed response data:', data);
                
                if (data.success) {
                    showToast('Transaksi berhasil!', 'success');
                    
                    // Show success modal instead of alert
                    showSuccessModal(data);
                } else {
                    throw new Error(data.message || 'Transaction failed');
                }
            })
            .catch(error => {
                console.error('Transaction error:', error);
                alert('Error: ' + error.message);
            })
            .finally(() => {
                // Always reset processing state
                isProcessing = false;
                updateButtonStates();
            });
        }
        
        // Show success modal
        function showSuccessModal(data) {
            const successHtml = `
                <div style="text-align: center; padding: 2rem;">
                    <i class="fas fa-check-circle" style="color: #16a34a; font-size: 4rem; margin-bottom: 1rem;"></i>
                    <h2 style="color: #16a34a; margin-bottom: 1rem;">Transaksi Berhasil!</h2>
                    <p style="font-size: 1.2rem; margin-bottom: 1rem;">
                        <strong>No. Invoice: ${data.invoice_no}</strong>
                    </p>
                    <p>Total: ${formatRupiah(data.total)}</p>
                    <p>Items: ${data.items_count}</p>
                    
                    <div style="margin-top: 2rem; display: flex; gap: 1rem; justify-content: center;">
                        <button onclick="printReceipt(${data.sale_id})" class="btn btn-primary">
                            <i class="fas fa-print"></i> Cetak Struk
                        </button>
                        <button onclick="completeSaleSuccess()" class="btn btn-success">
                            <i class="fas fa-arrow-right"></i> Transaksi Baru
                        </button>
                    </div>
                </div>
            `;
            
            document.querySelector('#paymentModal .modal-content').innerHTML = successHtml;
        }
        
        // Print receipt
        function printReceipt(saleId) {
            window.open('print_receipt.php?id=' + saleId, '_blank');
            completeSaleSuccess();
        }
        
        // Handle successful sale completion
        function completeSaleSuccess() {
            // Clear cart
            cart = [];
            updateCart();
            
            // Close modal
            document.getElementById('paymentModal').style.display = 'none';
            
            // Reset modal content
            location.reload(); // Simple way to reset everything
            
            console.log('Sale completed successfully');
        }
        
        // Show toast notification
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#16a34a' : type === 'error' ? '#dc2626' : '#2563eb'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
        
        // Enhanced window click handler
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target === modal && !isProcessing) {
                closePaymentModal();
            }
        }
        
        // Prevent accidental page refresh
        window.addEventListener('beforeunload', function(e) {
            if (cart.length > 0) {
                e.preventDefault();
                e.returnValue = 'Ada item di keranjang yang belum diproses. Yakin ingin keluar?';
            }
        });
        
        // Enhanced keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape' && !isProcessing) {
                closePaymentModal();
                hideSearchResults();
            }
            
            // F2 to focus barcode input
            if (e.key === 'F2') {
                e.preventDefault();
                document.getElementById('barcodeInput').focus();
            }
            
            // F4 to process payment
            if (e.key === 'F4' && cart.length > 0) {
                e.preventDefault();
                processPayment();
            }
            
            // Enter in payment modal to complete sale
            if (e.key === 'Enter' && document.getElementById('paymentModal').style.display === 'block') {
                if (e.target.id === 'paymentAmount') {
                    completeSale();
                }
            }
        });
        
        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
<?php $conn->close(); ?>