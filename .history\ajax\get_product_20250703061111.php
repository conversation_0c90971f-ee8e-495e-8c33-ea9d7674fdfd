<?php
// ajax/get_product.php - Test version with error handling
require_once '../config/database.php';

header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

$id = (int)($_GET['id'] ?? 0);

if ($id > 0) {
    try {
        $conn = connectDB();
        $sql = "SELECT * FROM products WHERE id = $id";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $product = $result->fetch_assoc();
            $product['price_formatted'] = formatRupiah($product['selling_price']);
            echo json_encode($product);
        } else {
            echo json_encode(['error' => 'Product not found']);
        }
        
        $conn->close();
    } catch (Exception $e) {
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['error' => 'Invalid ID']);
}
?>