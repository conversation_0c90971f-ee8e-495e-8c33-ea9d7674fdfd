// assets/js/main.js - K5N Apps JavaScript Utilities

// Format number to Rupiah
function formatRupiah(angka) {
    return 'Rp ' + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Parse Rupiah string to number
function parseRupiah(rupiah) {
    return parseInt(rupiah.replace(/[^0-9]/g, ''));
}

// Show loading spinner
function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'spinner';
    loading.id = 'loadingSpinner';
    document.body.appendChild(loading);
}

// Hide loading spinner
function hideLoading() {
    const loading = document.getElementById('loadingSpinner');
    if (loading) {
        loading.remove();
    }
}

// Show alert message
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    // Insert at the beginning of content
    const content = document.querySelector('.content');
    content.insertBefore(alertDiv, content.firstChild);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Confirm dialog
function confirmAction(message) {
    return confirm(message);
}

// AJAX helper function
async function ajaxRequest(url, options = {}) {
    try {
        showLoading();
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        });
        
        hideLoading();
        
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        return await response.json();
    } catch (error) {
        hideLoading();
        console.error('Ajax error:', error);
        showAlert('Terjadi kesalahan. Silakan coba lagi.', 'danger');
        return null;
    }
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Product search with debounce
const searchProducts = debounce(function(keyword) {
    if (keyword.length < 2) return;
    
    ajaxRequest(`ajax/search_products.php?q=${keyword}`)
        .then(data => {
            if (data && data.products) {
                displaySearchResults(data.products);
            }
        });
}, 300);

// Display search results
function displaySearchResults(products) {
    const resultsDiv = document.getElementById('searchResults');
    if (!resultsDiv) return;
    
    let html = '';
    products.forEach(product => {
        html += `
            <div class="search-result-item" onclick="selectProduct(${product.id})">
                <strong>${product.name}</strong>
                <span>${product.code} - ${formatRupiah(product.selling_price)}</span>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
    resultsDiv.style.display = products.length > 0 ? 'block' : 'none';
}

// Print function with custom settings
function printElement(elementId) {
    const printContent = document.getElementById(elementId).innerHTML;
    const originalContent = document.body.innerHTML;
    
    document.body.innerHTML = printContent;
    window.print();
    document.body.innerHTML = originalContent;
    
    // Reload page to restore event listeners
    location.reload();
}

// Date range picker helper
function setDateRange(range) {
    const today = new Date();
    let startDate, endDate;
    
    switch(range) {
        case 'today':
            startDate = endDate = today;
            break;
        case 'yesterday':
            startDate = endDate = new Date(today.setDate(today.getDate() - 1));
            break;
        case 'week':
            startDate = new Date(today.setDate(today.getDate() - 7));
            endDate = new Date();
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
    }
    
    document.getElementById('start_date').value = formatDate(startDate);
    document.getElementById('end_date').value = formatDate(endDate);
}

// Format date for input
function formatDate(date) {
    const d = new Date(date);
    const month = ('0' + (d.getMonth() + 1)).slice(-2);
    const day = ('0' + d.getDate()).slice(-2);
    return d.getFullYear() + '-' + month + '-' + day;
}

// Auto logout after inactivity
let inactivityTimer;
const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes

function resetInactivityTimer() {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(() => {
        if (confirm('Sesi Anda akan berakhir karena tidak ada aktivitas. Lanjutkan?')) {
            resetInactivityTimer();
        } else {
            window.location.href = 'logout.php';
        }
    }, INACTIVITY_TIMEOUT);
}

// Monitor user activity
document.addEventListener('DOMContentLoaded', function() {
    // Reset timer on user activity
    ['mousedown', 'keypress', 'scroll', 'touchstart