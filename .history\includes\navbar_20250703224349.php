<?php
// includes/navbar.php
?>
<nav class="navbar">
    <div class="navbar-brand">
        <button class="btn" onclick="toggleSidebar()" style="background: none; color: var(--dark-color); font-size: 1.25rem;">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    
    <div class="navbar-nav">
        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <span><?php echo $_SESSION['full_name']; ?></span>
            <span style="background-color: var(--primary-color); color: white; padding: 0.125rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">
                <?php echo ucfirst($_SESSION['role']); ?>
            </span>
        </div>
        
        <button class="btn" onclick="toggleNotifications()" style="background: none; color: var(--dark-color); font-size: 1.25rem; position: relative;">
            <i class="fas fa-bell"></i>
            <?php
            // Check for low stock notifications
            $conn = connectDB();
            $result = $conn->query("SELECT COUNT(*) as total FROM products WHERE stock <= min_stock");
            $notif_count = $result->fetch_assoc()['total'];
            if ($notif_count > 0):
            ?>
            <span style="position: absolute; top: -5px; right: -5px; background-color: var(--danger-color); color: white; font-size: 0.7rem; padding: 0.125rem 0.375rem; border-radius: 9999px;">
                <?php echo $notif_count; ?>
            </span>
            <?php endif; ?>
        </button>
    </div>
</nav>

<script>
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('active');
}

function toggleNotifications() {
    alert('Ada <?php echo $notif_count; ?> produk dengan stok menipis!');
}
</script>