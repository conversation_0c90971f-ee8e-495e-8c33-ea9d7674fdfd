<?php
// setup_sample_data.php - Menambahkan sample data
session_start();

// Set sebagai admin untuk testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

require_once 'config/database.php';

echo "<h2>Setup Sample Data - K5N Inventory</h2>";

try {
    $conn = connectDB();
    
    // Check existing data
    echo "<h3>1. Checking Current Data</h3>";
    
    $categories_count = $conn->query("SELECT COUNT(*) as count FROM categories")->fetch_assoc()['count'];
    $products_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    $users_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    
    echo "Categories: $categories_count<br>";
    echo "Products: $products_count<br>";
    echo "Users: $users_count<br><br>";
    
    // Add sample categories if empty
    echo "<h3>2. Setting up Categories</h3>";
    if ($categories_count == 0) {
        $categories = [
            ['name' => 'Elektronik', 'description' => 'Komponen dan perangkat elektronik'],
            ['name' => 'Aksesoris', 'description' => 'Aksesoris dan pelengkap'],
            ['name' => 'Spare Part', 'description' => 'Suku cadang dan komponen']
        ];
        
        foreach ($categories as $cat) {
            $name = escape($conn, $cat['name']);
            $desc = escape($conn, $cat['description']);
            $conn->query("INSERT INTO categories (name, description) VALUES ('$name', '$desc')");
            echo "✅ Added category: {$cat['name']}<br>";
        }
    } else {
        echo "✅ Categories already exist<br>";
    }
    
    // Add sample products
    echo "<h3>3. Setting up Products</h3>";
    if ($products_count < 5) {
        // Get category IDs
        $cat_result = $conn->query("SELECT id, name FROM categories ORDER BY id");
        $categories = [];
        while ($row = $cat_result->fetch_assoc()) {
            $categories[] = $row;
        }
        
        if (count($categories) > 0) {
            $sample_products = [
                [
                    'name' => 'Power Bank 10000mAh',
                    'category_id' => $categories[0]['id'],
                    'purchase_price' => 75000,
                    'selling_price' => 125000,
                    'stock' => 25,
                    'min_stock' => 5,
                    'description' => 'Power bank portable 10000mAh dengan dual USB'
                ],
                [
                    'name' => 'Kabel USB Type-C',
                    'category_id' => $categories[1]['id'],
                    'purchase_price' => 15000,
                    'selling_price' => 25000,
                    'stock' => 50,
                    'min_stock' => 10,
                    'description' => 'Kabel USB Type-C 1 meter fast charging'
                ],
                [
                    'name' => 'Earphone Bluetooth',
                    'category_id' => $categories[0]['id'],
                    'purchase_price' => 45000,
                    'selling_price' => 75000,
                    'stock' => 15,
                    'min_stock' => 3,
                    'description' => 'Earphone wireless Bluetooth 5.0'
                ],
                [
                    'name' => 'Charger Fast Charging',
                    'category_id' => $categories[1]['id'],
                    'purchase_price' => 35000,
                    'selling_price' => 55000,
                    'stock' => 30,
                    'min_stock' => 5,
                    'description' => 'Charger fast charging 18W USB-C'
                ],
                [
                    'name' => 'Casing HP Transparent',
                    'category_id' => count($categories) > 2 ? $categories[2]['id'] : $categories[1]['id'],
                    'purchase_price' => 8000,
                    'selling_price' => 15000,
                    'stock' => 100,
                    'min_stock' => 20,
                    'description' => 'Casing HP transparan anti-shock'
                ]
            ];
            
            foreach ($sample_products as $product) {
                // Generate product code
                $cat_name = '';
                foreach ($categories as $cat) {
                    if ($cat['id'] == $product['category_id']) {
                        $cat_name = $cat['name'];
                        break;
                    }
                }
                
                $code = generateProductCode($conn, $cat_name);
                $barcode = 'K5N' . $code . rand(1000, 9999);
                
                $name = escape($conn, $product['name']);
                $desc = escape($conn, $product['description']);
                
                $sql = "INSERT INTO products (code, name, category_id, barcode, purchase_price, selling_price, stock, min_stock, description) 
                        VALUES ('$code', '$name', {$product['category_id']}, '$barcode', {$product['purchase_price']}, {$product['selling_price']}, {$product['stock']}, {$product['min_stock']}, '$desc')";
                
                if ($conn->query($sql)) {
                    echo "✅ Added product: {$product['name']} (Code: $code, Stock: {$product['stock']})<br>";
                } else {
                    echo "❌ Failed to add product: {$product['name']} - " . $conn->error . "<br>";
                }
            }
        } else {
            echo "❌ No categories found. Please add categories first.<br>";
        }
    } else {
        echo "✅ Products already exist<br>";
    }
    
    // Add sample user if needed
    echo "<h3>4. Setting up Users</h3>";
    if ($users_count == 0) {
        $password_hash = password_hash('admin', PASSWORD_DEFAULT);
        $conn->query("INSERT INTO users (username, password, full_name, role) VALUES ('admin', '$password_hash', 'Administrator', 'admin')");
        echo "✅ Added admin user (username: admin, password: admin)<br>";
        
        $password_hash = password_hash('kasir', PASSWORD_DEFAULT);
        $conn->query("INSERT INTO users (username, password, full_name, role) VALUES ('kasir', '$password_hash', 'Kasir', 'user')");
        echo "✅ Added kasir user (username: kasir, password: kasir)<br>";
    } else {
        echo "✅ Users already exist<br>";
    }
    
    // Final summary
    echo "<h3>5. Final Summary</h3>";
    $final_categories = $conn->query("SELECT COUNT(*) as count FROM categories")->fetch_assoc()['count'];
    $final_products = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    $final_users = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $products_with_stock = $conn->query("SELECT COUNT(*) as count FROM products WHERE stock > 0")->fetch_assoc()['count'];
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>✅ Setup Complete!</h4>";
    echo "<strong>Categories:</strong> $final_categories<br>";
    echo "<strong>Products:</strong> $final_products<br>";
    echo "<strong>Products with stock:</strong> $products_with_stock<br>";
    echo "<strong>Users:</strong> $final_users<br>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='test_existing_structure.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Test System</a>";
    echo "<a href='sales.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Open Sales</a>";
    echo "<a href='products.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Manage Products</a>";
    echo "</div>";
    
    // Show sample products
    echo "<h3>6. Current Products</h3>";
    $products_result = $conn->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id");
    
    if ($products_result->num_rows > 0) {
        echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa; border: 1px solid #dee2e6;'>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>ID</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Code</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Name</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Category</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Price</th>";
        echo "<th style='padding: 8px; border: 1px solid #dee2e6; text-align: left;'>Stock</th>";
        echo "</tr>";
        
        while ($product = $products_result->fetch_assoc()) {
            $stock_color = $product['stock'] <= $product['min_stock'] ? 'color: #dc3545; font-weight: bold;' : '';
            echo "<tr style='border: 1px solid #dee2e6;'>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>{$product['id']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>{$product['code']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>{$product['name']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>{$product['category_name']}</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6;'>" . formatRupiah($product['selling_price']) . "</td>";
            echo "<td style='padding: 8px; border: 1px solid #dee2e6; $stock_color'>{$product['stock']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 10px 0; color: #721c24;'>";
    echo "<h4>❌ Error</h4>";
    echo $e->getMessage();
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; max-width: 1200px; }
h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
h3 { color: #666; margin-top: 20px; border-left: 4px solid #007cba; padding-left: 10px; }
h4 { margin-bottom: 10px; }
a:hover { opacity: 0.9; }
table { font-size: 14px; }
</style>