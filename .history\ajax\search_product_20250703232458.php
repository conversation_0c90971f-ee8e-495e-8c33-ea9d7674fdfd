<?php
// ================================================================
// ajax/search_product.php - Enhanced Database Compatible
// ================================================================
?>
<?php
// ajax/search_product.php - Search by barcode/code
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
checkLogin();

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$barcode = $_GET['barcode'] ?? '';

if (empty($barcode)) {
    echo json_encode(['success' => false, 'message' => 'Barcode tidak boleh kosong']);
    exit;
}

try {
    $conn = connectDB();
    
    // Enhanced search with category and supplier info
    $stmt = $conn->prepare("
        SELECT 
            p.*,
            c.name as category_name,
            s.name as supplier_name
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE (p.barcode = ? OR p.code = ? OR p.name LIKE ?) 
        AND p.is_active = TRUE 
        AND p.stock > 0
        ORDER BY 
            CASE 
                WHEN p.barcode = ? THEN 1
                WHEN p.code = ? THEN 2
                ELSE 3
            END
        LIMIT 1
    ");
    
    $search_term = "%$barcode%";
    $stmt->bind_param("ssssss", $barcode, $barcode, $search_term, $barcode, $barcode);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // Add computed fields
        $row['price_formatted'] = formatRupiah($row['selling_price']);
        $row['stock_status'] = $row['stock'] <= $row['min_stock'] ? 'low' : 'normal';
        
        echo json_encode([
            'success' => true,
            'product' => $row
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Produk tidak ditemukan atau stok habis'
        ]);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    error_log("Barcode search error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Terjadi kesalahan sistem'
    ]);
}
?>
