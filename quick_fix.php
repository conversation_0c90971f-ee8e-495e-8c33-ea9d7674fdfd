<?php
// quick_fix.php - Automatic session fix
echo "<h1>🔧 Quick Session Fix Tool</h1>";

echo "<h2>Checking Files...</h2>";

// Files to check
$files_to_check = [
    'sales.php',
    'products.php', 
    'ajax/process_sale.php',
    'ajax/search_product.php',
    'ajax/get_product.php'
];

$issues_found = [];
$fixes_applied = [];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<h3>Checking: $file</h3>";
        
        $content = file_get_contents($file);
        $original_content = $content;
        $has_issues = false;
        
        // Check for direct session_start() calls
        if (preg_match('/^\s*session_start\(\)\s*;/m', $content)) {
            echo "❌ Found direct session_start() call<br>";
            $has_issues = true;
            $issues_found[] = "$file: Direct session_start() call";
            
            // Fix: Replace direct session_start() with conditional
            $content = preg_replace(
                '/^\s*session_start\(\)\s*;/m',
                'if (session_status() == PHP_SESSION_NONE) {
    session_start();
}',
                $content
            );
        }
        
        // Check for session_start() at beginning of file
        if (preg_match('/^<\?php\s*session_start\(\)/m', $content)) {
            echo "❌ Found session_start() at file beginning<br>";
            $has_issues = true;
            $issues_found[] = "$file: session_start() at file beginning";
            
            // Fix: Replace with conditional
            $content = preg_replace(
                '/^(<\?php\s*)session_start\(\)\s*;/m',
                '$1if (session_status() == PHP_SESSION_NONE) {
    session_start();
}',
                $content
            );
        }
        
        // Check if includes/functions.php is included before session operations
        if (strpos($content, 'includes/functions.php') !== false && 
            strpos($content, 'session_start') !== false) {
            
            $func_pos = strpos($content, 'includes/functions.php');
            $session_pos = strpos($content, 'session_start');
            
            if ($session_pos < $func_pos) {
                echo "⚠️ session_start() called before includes/functions.php<br>";
                $issues_found[] = "$file: Session started before includes loaded";
            }
        }
        
        // Apply fixes if content changed
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "✅ Fixed: $file<br>";
                $fixes_applied[] = $file;
            } else {
                echo "❌ Failed to write fix to: $file<br>";
            }
        } else if (!$has_issues) {
            echo "✅ No issues found in: $file<br>";
        }
        
    } else {
        echo "⚠️ File not found: $file<br>";
    }
}

echo "<h2>Summary</h2>";

if (count($issues_found) > 0) {
    echo "<h3>Issues Found:</h3>";
    echo "<ul>";
    foreach ($issues_found as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
} else {
    echo "✅ No session issues found!<br>";
}

if (count($fixes_applied) > 0) {
    echo "<h3>Fixes Applied:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>Fixed: $fix</li>";
    }
    echo "</ul>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 15px 0; color: #155724;'>";
    echo "<strong>✅ Fixes Applied Successfully!</strong><br>";
    echo "Please test your pages now to ensure everything works correctly.";
    echo "</div>";
} else {
    echo "<div style='background: #e7f3ff; border: 1px solid #b8daff; border-radius: 5px; padding: 15px; margin: 15px 0; color: #004085;'>";
    echo "<strong>ℹ️ No fixes needed</strong><br>";
    echo "All files appear to be correctly configured.";
    echo "</div>";
}

echo "<h2>Manual Check Required</h2>";
echo "<p>Please manually verify these common patterns in your files:</p>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 15px 0; color: #856404;'>";
echo "<h4>✅ Good Patterns:</h4>";
echo "<pre>";
echo htmlspecialchars('// For main pages
<?php
require_once \'config/database.php\';
checkLogin();

// For AJAX files
<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once \'../config/database.php\';');
echo "</pre>";

echo "<h4>❌ Bad Patterns:</h4>";
echo "<pre>";
echo htmlspecialchars('<?php
session_start(); // Direct call without check

<?php session_start(); // At very beginning');
echo "</pre>";
echo "</div>";

echo "<h2>Test Your Fixes</h2>";
echo "<ol>";
echo "<li><a href='session_debug.php' style='color: #007cba; text-decoration: none; font-weight: bold;'>Run Session Debug</a> - Check session status</li>";
echo "<li><a href='system_status.php' style='color: #28a745; text-decoration: none; font-weight: bold;'>Check System Status</a> - Overall system health</li>";
echo "<li><a href='sales.php' style='color: #17a2b8; text-decoration: none; font-weight: bold;'>Test Sales Page</a> - Verify modal works</li>";
echo "</ol>";

echo "<h2>Still Having Issues?</h2>";
echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 15px 0; color: #721c24;'>";
echo "<h4>Common Remaining Issues:</h4>";
echo "<ul>";
echo "<li><strong>XAMPP not running:</strong> Start Apache and MySQL in XAMPP Control Panel</li>";
echo "<li><strong>Database not found:</strong> Create 'k5n_inventory' database in phpMyAdmin</li>";
echo "<li><strong>Functions missing:</strong> Check if includes/functions.php exists and is included</li>";
echo "<li><strong>Permissions:</strong> Ensure PHP can write to files (for auto-fix)</li>";
echo "</ul>";
echo "</div>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; max-width: 1000px; }";
echo "h1 { color: #dc3545; border-bottom: 3px solid #dc3545; padding-bottom: 15px; }";
echo "h2 { color: #495057; margin-top: 30px; border-left: 4px solid #007cba; padding-left: 15px; }";
echo "h3 { color: #666; margin-top: 20px; }";
echo "pre { background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; overflow-x: auto; }";
echo "ul, ol { margin-left: 20px; }";
echo "li { margin-bottom: 5px; }";
echo "a:hover { text-decoration: underline !important; }";
echo "</style>";
?>