<?php
// dashboard.php - Main Dashboard
require_once 'config/database.php';
checkLogin();

$conn = connectDB();

// Get statistics
$stats = [];

// Total products
$result = $conn->query("SELECT COUNT(*) as total FROM products");
$stats['products'] = $result->fetch_assoc()['total'];

// Low stock products
$result = $conn->query("SELECT COUNT(*) as total FROM products WHERE stock <= min_stock");
$stats['low_stock'] = $result->fetch_assoc()['total'];

// Today's sales
$result = $conn->query("SELECT COUNT(*) as total, COALESCE(SUM(total_amount), 0) as amount FROM sales WHERE DATE(created_at) = CURDATE()");
$row = $result->fetch_assoc();
$stats['sales_today'] = $row['total'];
$stats['sales_amount'] = $row['amount'];

// Get recent sales
$recent_sales = $conn->query("
    SELECT s.*, u.full_name as cashier 
    FROM sales s 
    JOIN users u ON s.user_id = u.id 
    ORDER BY s.created_at DESC 
    LIMIT 5
");

// Get low stock products
$low_stock = $conn->query("
    SELECT * FROM products 
    WHERE stock <= min_stock 
    ORDER BY stock ASC 
    LIMIT 5
");

// Get sales chart data (last 7 days)
$chart_data = [];
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $result = $conn->query("
        SELECT COALESCE(SUM(total_amount), 0) as total 
        FROM sales 
        WHERE DATE(created_at) = '$date'
    ");
    $chart_data[] = [
        'date' => date('d M', strtotime($date)),
        'amount' => $result->fetch_assoc()['total']
    ];
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Navbar -->
            <?php include 'includes/navbar.php'; ?>
            
            <!-- Content -->
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Dashboard</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Dashboard</span>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['products']; ?></h3>
                            <p>Total Produk</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['low_stock']; ?></h3>
                            <p>Stok Menipis</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo $stats['sales_today']; ?></h3>
                            <p>Penjualan Hari Ini</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-danger">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo formatRupiah($stats['sales_amount']); ?></h3>
                            <p>Omzet Hari Ini</p>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Tables -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-top: 2rem;">
                    <!-- Sales Chart -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Grafik Penjualan (7 Hari Terakhir)</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- Low Stock Alert -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Peringatan Stok Menipis</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Kode</th>
                                            <th>Nama Produk</th>
                                            <th>Stok</th>
                                            <th>Min. Stok</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($row = $low_stock->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $row['code']; ?></td>
                                            <td><?php echo $row['name']; ?></td>
                                            <td>
                                                <span style="color: var(--danger-color); font-weight: bold;">
                                                    <?php echo $row['stock']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $row['min_stock']; ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Sales -->
                <div class="card" style="margin-top: 1.5rem;">
                    <div class="card-header">
                        <h3>Penjualan Terakhir</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>No. Invoice</th>
                                        <th>Tanggal</th>
                                        <th>Kasir</th>
                                        <th>Total</th>
                                        <th>Metode</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($row = $recent_sales->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $row['invoice_no']; ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                        <td><?php echo $row['cashier']; ?></td>
                                        <td><?php echo formatRupiah($row['total_amount']); ?></td>
                                        <td>
                                            <span class="badge" style="background-color: var(--primary-color); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">
                                                <?php echo ucfirst($row['payment_method']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const chartData = <?php echo json_encode($chart_data); ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.map(item => item.date),
                datasets: [{
                    label: 'Penjualan',
                    data: chartData.map(item => item.amount),
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>