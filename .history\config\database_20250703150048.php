<?php
// config/database.php - Konfigurasi database

define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', ''); // Kosong untuk XAMPP default
define('DB_NAME', 'k5n_inventory');

// Fungsi koneksi database
function connectDB() {
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        // Set charset to utf8
        $conn->set_charset("utf8");
        
        return $conn;
    } catch (Exception $e) {
        die("Database connection error: " . $e->getMessage());
    }
}

// Fungsi untuk escape string
function escape($conn, $str) {
    return $conn->real_escape_string($str);
}

// Fungsi untuk generate invoice number
function generateInvoiceNo($conn) {
    $prefix = "INV";
    $date = date("Ymd");
    
    // Get last invoice for today
    $sql = "SELECT invoice_no FROM sales 
            WHERE DATE(created_at) = CURDATE() 
            ORDER BY id DESC LIMIT 1";
    
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $lastNo = $row['invoice_no'];
        $sequence = intval(substr($lastNo, -4)) + 1;
    } else {
        $sequence = 1;
    }
    
    return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
}

// Fungsi untuk generate product code
function generateProductCode($conn, $categoryName) {
    $prefix = strtoupper(substr($categoryName, 0, 3));
    
    $sql = "SELECT code FROM products 
            WHERE code LIKE '$prefix%' 
            ORDER BY id DESC LIMIT 1";
    
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $lastCode = $row['code'];
        $sequence = intval(substr($lastCode, -4)) + 1;
    } else {
        $sequence = 1;
    }
    
    return $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);
}

// Fungsi untuk format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}

// Fungsi untuk cek login
session_start();

function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header("Location: index.php");
        exit();
    }
}

function checkAdmin() {
    checkLogin();
    if ($_SESSION['role'] != 'admin') {
        $_SESSION['error'] = "Akses ditolak! Hanya admin yang diizinkan.";
        header("Location: dashboard.php");
        exit();
    }
}

// Fungsi untuk log aktivitas
function logActivity($conn, $activity, $reference = null) {
    $user_id = $_SESSION['user_id'] ?? 0;
    $activity = escape($conn, $activity);
    $reference = $reference ? escape($conn, $reference) : null;
    
    $sql = "INSERT INTO activity_logs (user_id, activity, reference) 
            VALUES ($user_id, '$activity', " . ($reference ? "'$reference'" : "NULL") . ")";
    
    $conn->query($sql);
}
?>