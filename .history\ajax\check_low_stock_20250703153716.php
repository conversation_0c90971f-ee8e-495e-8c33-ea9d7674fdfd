<?php
// ajax/check_low_stock.php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once '../config/database.php';

header('Content-Type: application/json');

$conn = connectDB();

$result = $conn->query("SELECT COUNT(*) as count FROM products WHERE stock <= min_stock");
$count = $result->fetch_assoc()['count'];

$products = [];
if ($count > 0) {
    $result = $conn->query("SELECT code, name, stock, min_stock FROM products WHERE stock <= min_stock LIMIT 5");
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }
}

echo json_encode([
    'count' => $count,
    'products' => $products
]);

$conn->close();
?>