<?php
// users.php (VERSI AMAN)
require_once 'config/database.php';
checkAdmin(); // <PERSON>ya admin yang bisa akses

$conn = connectDB();
$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    // --- AKSI TAMBAH USER ---
    if ($action == 'add') {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $full_name = $_POST['full_name'];
        $role = $_POST['role'];

        // Cek username dulu
        $stmt_check = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt_check->bind_param("s", $username);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            $message = '<div class="alert alert-danger">Username sudah digunakan!</div>';
        } else {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $username, $hashed_password, $full_name, $role);
            
            if ($stmt->execute()) {
                $message = '<div class="alert alert-success">User berhasil ditambahkan!</div>';
            } else {
                $message = '<div class="alert alert-danger">Error: ' . $stmt->error . '</div>';
            }
            $stmt->close();
        }
        $stmt_check->close();
    }
    
    // --- AKSI EDIT USER ---
    if ($action == 'edit') {
        $id = (int)$_POST['id'];
        $full_name = $_POST['full_name'];
        $role = $_POST['role'];
        
        // Update password jika diisi
        if (!empty($_POST['password'])) {
            $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE users SET full_name = ?, role = ?, password = ? WHERE id = ?");
            $stmt->bind_param("sssi", $full_name, $role, $hashed_password, $id);
        } else {
            // Update tanpa password
            $stmt = $conn->prepare("UPDATE users SET full_name = ?, role = ? WHERE id = ?");
            $stmt->bind_param("ssi", $full_name, $role, $id);
        }
        
        if ($stmt->execute()) {
            $message = '<div class="alert alert-success">User berhasil diupdate!</div>';
        } else {
            $message = '<div class="alert alert-danger">Error: ' . $stmt->error . '</div>';
        }
        $stmt->close();
    }
    
    // --- AKSI HAPUS USER ---
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        // Logika validasi sudah bagus, kita pertahankan
        if ($id == $_SESSION['user_id']) {
            $message = '<div class="alert alert-danger">Tidak bisa menghapus akun sendiri!</div>';
        } else {
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $id);
            if ($stmt->execute()) {
                $message = '<div class="alert alert-success">User berhasil dihapus!</div>';
            }
            $stmt->close();
        }
    }
}

// Get all users
$users = $conn->query("SELECT id, username, full_name, role, created_at FROM users ORDER BY full_name");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen User - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="card">
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Username</th>
                                    <th>Nama Lengkap</th>
                                    <th>Role</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $users->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?php echo $user['role'] == 'admin' ? 'var(--primary-color)' : 'var(--success-color)'; ?>; color: white;">
                                            <?php echo htmlspecialchars(ucfirst($user['role'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm" onclick='editUser(<?php echo htmlspecialchars(json_encode($user), ENT_QUOTES, "UTF-8"); ?>)'>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Yakin hapus user ini?')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo (int)$user['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i></button>
                                        </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    </body>
</html>
<?php $conn->close(); ?>