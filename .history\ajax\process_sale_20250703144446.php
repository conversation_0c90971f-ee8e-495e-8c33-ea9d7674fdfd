<?php
// ajax/process_sale.php - FIXED VERSION
require_once '../config/database.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Enable error logging for debugging
error_reporting(E_ALL);
ini_set('log_errors', 1);

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not logged in');
    }
    
    // Get POST data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required data
    if (empty($data['items']) || !isset($data['total']) || !is_array($data['items'])) {
        throw new Exception('Invalid data: items or total missing');
    }
    
    if (count($data['items']) === 0) {
        throw new Exception('No items in cart');
    }
    
    $conn = connectDB();
    
    // Start transaction
    $conn->begin_transaction();
    
    // Sanitize and validate input
    $user_id = intval($_SESSION['user_id']);
    $total = floatval($data['total']);
    $payment_method = isset($data['payment_method']) ? escape($conn, $data['payment_method']) : 'cash';
    $payment_received = floatval($data['payment_received'] ?? 0);
    $change_amount = floatval($data['change_amount'] ?? 0);
    
    // Validate payment
    if ($payment_received < $total) {
        throw new Exception('Payment amount is less than total');
    }
    
    // Generate invoice number
    $invoice_no = generateInvoiceNo($conn);
    
    // Insert sale record
    $stmt = $conn->prepare("INSERT INTO sales (invoice_no, user_id, total_amount, payment_method, payment_received, change_amount) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sidsdd", $invoice_no, $user_id, $total, $payment_method, $payment_received, $change_amount);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to create sale record: ' . $stmt->error);
    }
    
    $sale_id = $conn->insert_id;
    
    // Insert sale items and update stock
    $stmt_item = $conn->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) VALUES (?, ?, ?, ?, ?)");
    $calculated_total = 0;
    
    foreach ($data['items'] as $item) {
        // Validate item data
        if (!isset($item['id']) || !isset($item['quantity']) || !isset($item['price'])) {
            throw new Exception('Invalid item data');
        }
        
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        $price = floatval($item['price']);
        $subtotal = $price * $quantity;
        
        // Validate quantities
        if ($quantity <= 0) {
            throw new Exception('Invalid quantity for product ID: ' . $product_id);
        }
        
        // Check stock availability
        $check_stock = $conn->prepare("SELECT stock, name FROM products WHERE id = ?");
        $check_stock->bind_param("i", $product_id);
        $check_stock->execute();
        $result = $check_stock->get_result();
        $product = $result->fetch_assoc();
        
        if (!$product) {
            throw new Exception('Product not found: ID ' . $product_id);
        }
        
        if ($product['stock'] < $quantity) {
            throw new Exception('Insufficient stock for ' . $product['name'] . '. Available: ' . $product['stock'] . ', Required: ' . $quantity);
        }
        
        // Insert sale item
        $stmt_item->bind_param("iiidd", $sale_id, $product_id, $quantity, $price, $subtotal);
        if (!$stmt_item->execute()) {
            throw new Exception('Failed to insert sale item: ' . $stmt_item->error);
        }
        
        $calculated_total += $subtotal;
    }
    
    // Verify calculated total matches sent total
    if (abs($calculated_total - $total) > 0.01) {
        throw new Exception('Total mismatch. Calculated: ' . $calculated_total . ', Sent: ' . $total);
    }
    
    // Commit transaction
    $conn->commit();
    
    // Success response
    echo json_encode([
        'success' => true,
        'sale_id' => $sale_id,
        'invoice_no' => $invoice_no,
        'message' => 'Transaction completed successfully',
        'total' => $total,
        'items_count' => count($data['items'])
    ]);
    
} catch (Exception $e) {
    // Rollback transaction if started
    if (isset($conn) && $conn) {
        $conn->rollback();
    }
    
    // Error response
    $error_response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
    
    // Log error for debugging
    error_log('Process Sale Error: ' . $e->getMessage());
    
    echo json_encode($error_response);
    
} finally {
    // Close connection
    if (isset($conn) && $conn) {
        $conn->close();
    }
}

/**
 * Generate unique invoice number
 */
function generateInvoiceNo($conn) {
    $today = date('Ymd');
    
    // Get last invoice number for today
    $stmt = $conn->prepare("SELECT invoice_no FROM sales WHERE DATE(created_at) = CURDATE() ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $last_invoice = $result->fetch_assoc()['invoice_no'];
        // Extract number from INV20250703XXXX format
        $last_number = intval(substr($last_invoice, -4));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return 'INV' . $today . str_pad($new_number, 4, '0', STR_PAD_LEFT);
}
?>