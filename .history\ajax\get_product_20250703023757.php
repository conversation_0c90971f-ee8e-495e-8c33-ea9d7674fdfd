<?php
// ajax/get_product.php
require_once '../config/database.php';

header('Content-Type: application/json');

$id = (int)($_GET['id'] ?? 0);

if ($id > 0) {
    $conn = connectDB();
    $sql = "SELECT * FROM products WHERE id = $id";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        $product = $result->fetch_assoc();
        $product['price_formatted'] = formatRupiah($product['selling_price']);
        echo json_encode($product);
    } else {
        echo json_encode(['error' => 'Product not found']);
    }
    
    $conn->close();
} else {
    echo json_encode(['error' => 'Invalid ID']);
}
?>