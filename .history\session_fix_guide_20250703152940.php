<?php
// session_fix_guide.php - Fix session issues across all files
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Fix Guide - K5N Apps</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        h1 { color: #dc3545; border-bottom: 3px solid #dc3545; padding-bottom: 15px; }
        h2 { color: #495057; margin-top: 30px; border-left: 4px solid #dc3545; padding-left: 15px; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .fix-item { background: #e7f3ff; border-left: 4px solid #007cba; padding: 15px; margin: 10px 0; }
        .btn { display: inline-block; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: 500; margin: 5px; }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Session Warning Fix Guide</h1>
        
        <div class="alert alert-danger">
            <strong>Error Found:</strong> <code>session_start(): Ignoring session_start() because a session is already active</code>
        </div>

        <h2>🔍 Problem Analysis</h2>
        <p>Masalah ini terjadi karena <code>session_start()</code> dipanggil berulang kali di berbagai file. PHP tidak mengizinkan session dimulai lebih dari sekali dalam satu request.</p>

        <h2>✅ Solution: Updated config/database.php</h2>
        <div class="alert alert-success">
            File <strong>config/database.php</strong> sudah diperbaiki dengan solusi berikut:
        </div>

        <div class="fix-item">
            <h4>Before (Problematic):</h4>
            <div class="code-block">
                <pre><code>session_start(); // ❌ Always called, causes warning</code></pre>
            </div>
            
            <h4>After (Fixed):</h4>
            <div class="code-block">
                <pre><code>// ✅ Only start session if not already active
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}</code></pre>
            </div>
        </div>

        <h2>🔧 Files That Need Similar Fix</h2>
        <div class="grid">
            <div class="fix-item">
                <h4>sales.php</h4>
                <p>Check if it starts with:</p>
                <div class="code-block">
                    <pre><code>&lt;?php
require_once 'config/database.php';
checkLogin(); // ✅ Good - uses database.php session</code></pre>
                </div>
            </div>
            
            <div class="fix-item">
                <h4>products.php</h4>
                <p>Should start with:</p>
                <div class="code-block">
                    <pre><code>&lt;?php
require_once 'config/database.php';
checkAdmin(); // ✅ Good - uses database.php session</code></pre>
                </div>
            </div>
            
            <div class="fix-item">
                <h4>AJAX Files</h4>
                <p>Should use:</p>
                <div class="code-block">
                    <pre><code>&lt;?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once '../config/database.php';</code></pre>
                </div>
            </div>
            
            <div class="fix-item">
                <h4>Other PHP Files</h4>
                <p>Pattern to follow:</p>
                <div class="code-block">
                    <pre><code>&lt;?php
// Always include database first
require_once 'config/database.php';
// Session is handled by database.php</code></pre>
                </div>
            </div>
        </div>

        <h2>🛠️ Quick Fix Commands</h2>
        
        <div class="alert alert-warning">
            <strong>Step 1:</strong> Replace your current <code>config/database.php</code> with the fixed version above.
        </div>

        <div class="alert alert-warning">
            <strong>Step 2:</strong> Check these common problematic patterns in your files:
        </div>

        <div class="code-block">
            <h4>❌ Remove these patterns:</h4>
            <pre><code>session_start(); // Direct call without check
&lt;?php session_start(); // At top of file without check</code></pre>
            
            <h4>✅ Replace with:</h4>
            <pre><code>if (session_status() == PHP_SESSION_NONE) {
    session_start();
}</code></pre>
        </div>

        <h2>📋 Verification Checklist</h2>
        
        <div class="fix-item">
            <input type="checkbox" id="check1"> <label for="check1">✅ Updated config/database.php with session fix</label><br><br>
            <input type="checkbox" id="check2"> <label for="check2">✅ Checked sales.php doesn't call session_start() directly</label><br><br>
            <input type="checkbox" id="check3"> <label for="check3">✅ Checked products.php doesn't call session_start() directly</label><br><br>
            <input type="checkbox" id="check4"> <label for="check4">✅ Fixed all AJAX files to check session status first</label><br><br>
            <input type="checkbox" id="check5"> <label for="check5">✅ Tested pages - no more session warnings</label>
        </div>

        <h2>🧪 Test After Fix</h2>
        
        <div class="grid">
            <div>
                <a href="system_status.php" class="btn btn-primary">1. System Status</a>
                <p>Check if session warnings are gone</p>
            </div>
            <div>
                <a href="sales.php" class="btn btn-success">2. Test Sales</a>
                <p>Verify sales page works without warnings</p>
            </div>
            <div>
                <a href="products.php" class="btn btn-danger">3. Test Products</a>
                <p>Check products page functionality</p>
            </div>
        </div>

        <h2>🔍 Debug Session Issues</h2>
        
        <div class="code-block">
            <h4>Add this debug code temporarily to check session status:</h4>
            <pre><code>&lt;?php
// Debug session status
echo "Session Status: " . session_status() . "&lt;br&gt;";
echo "PHP_SESSION_DISABLED: " . PHP_SESSION_DISABLED . "&lt;br&gt;";
echo "PHP_SESSION_NONE: " . PHP_SESSION_NONE . "&lt;br&gt;";
echo "PHP_SESSION_ACTIVE: " . PHP_SESSION_ACTIVE . "&lt;br&gt;";

if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ Session is ACTIVE&lt;br&gt;";
    echo "Session ID: " . session_id() . "&lt;br&gt;";
} else {
    echo "❌ Session is NOT ACTIVE&lt;br&gt;";
}
?&gt;</code></pre>
        </div>

        <h2>📚 Best Practices</h2>
        
        <div class="alert alert-success">
            <h4>Moving Forward:</h4>
            <ul>
                <li><strong>Always include database.php first</strong> - It handles session properly</li>
                <li><strong>Never call session_start() directly</strong> - Use the helper functions</li>
                <li><strong>Use session helper functions:</strong> <code>isLoggedIn()</code>, <code>isAdmin()</code>, <code>getCurrentUser()</code></li>
                <li><strong>For AJAX files:</strong> Always check session status before starting</li>
                <li><strong>Use checkLogin() and checkAdmin()</strong> - They handle session properly</li>
            </ul>
        </div>

        <h2>🎯 Expected Result</h2>
        
        <div class="alert alert-success">
            After applying the fix, you should see:
            <ul>
                <li>✅ No more session warnings in PHP error log</li>
                <li>✅ Sales page loads without errors</li>
                <li>✅ Modal works properly</li>
                <li>✅ AJAX requests work correctly</li>
                <li>✅ User authentication functions properly</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="system_status.php" class="btn btn-primary">Check System Status Now</a>
        </div>
    </div>
</body>
</html>