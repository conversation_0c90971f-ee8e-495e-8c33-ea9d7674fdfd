<?php
// ajax/get_product.php - Get product details by ID for Enhanced Database
// Create this file in ajax/ folder

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
checkLogin();

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Enable error reporting for debugging but don't display in JSON
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

$id = (int)($_GET['id'] ?? 0);

if ($id <= 0) {
    echo json_encode([
        'error' => 'Invalid product ID',
        'message' => 'Product ID must be a positive integer',
        'timestamp' => date('c')
    ]);
    exit;
}

try {
    $conn = connectDB();
    
    // Enhanced query with comprehensive product information
    $stmt = $conn->prepare("
        SELECT 
            p.*,
            c.name as category_name,
            c.code as category_code,
            s.name as supplier_name,
            s.contact_person as supplier_contact,
            s.phone as supplier_phone,
            CASE 
                WHEN p.stock <= 0 THEN 'out_of_stock'
                WHEN p.stock <= p.min_stock THEN 'low_stock'
                WHEN p.stock >= p.max_stock THEN 'overstock'
                ELSE 'normal'
            END as stock_status,
            -- Calculate days since last update
            DATEDIFF(NOW(), p.updated_at) as days_since_update,
            -- Calculate profit margin
            CASE 
                WHEN p.purchase_price > 0 THEN 
                    ROUND((p.selling_price - p.purchase_price) / p.selling_price * 100, 2)
                ELSE 0 
            END as profit_margin_percentage
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.id = ? AND p.is_active = TRUE
    ");
    
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $product = $result->fetch_assoc();
        
        // Add computed fields for frontend
        $product['price_formatted'] = formatRupiah($product['selling_price']);
        $product['purchase_price_formatted'] = formatRupiah($product['purchase_price']);
        $product['wholesale_price_formatted'] = formatRupiah($product['wholesale_price']);
        $product['discount_price_formatted'] = formatRupiah($product['discount_price']);
        
        // Calculate profit amounts
        $product['profit_amount'] = $product['selling_price'] - $product['purchase_price'];
        $product['profit_amount_formatted'] = formatRupiah($product['profit_amount']);
        
        // Parse JSON fields if they exist
        if (!empty($product['gallery'])) {
            $product['gallery'] = json_decode($product['gallery'], true);
        } else {
            $product['gallery'] = [];
        }
        
        // Add stock analysis
        $product['stock_analysis'] = [
            'current_stock' => (int)$product['stock'],
            'min_stock' => (int)$product['min_stock'],
            'max_stock' => (int)$product['max_stock'],
            'reorder_point' => (int)$product['reorder_point'],
            'stock_percentage' => $product['max_stock'] > 0 ? 
                round(($product['stock'] / $product['max_stock']) * 100, 2) : 0,
            'needs_reorder' => $product['stock'] <= $product['reorder_point'],
            'is_low_stock' => $product['stock'] <= $product['min_stock'],
            'is_out_of_stock' => $product['stock'] <= 0,
            'is_overstock' => $product['stock'] >= $product['max_stock']
        ];
        
        // Add pricing analysis
        $product['pricing_analysis'] = [
            'has_wholesale_price' => $product['wholesale_price'] > 0,
            'has_discount_price' => $product['discount_price'] > 0,
            'margin_percentage' => $product['profit_margin_percentage'],
            'margin_rating' => $product['profit_margin_percentage'] >= 30 ? 'high' : 
                              ($product['profit_margin_percentage'] >= 15 ? 'medium' : 'low')
        ];
        
        // Add additional metadata
        $product['metadata'] = [
            'created_date' => date('d/m/Y', strtotime($product['created_at'])),
            'updated_date' => date('d/m/Y', strtotime($product['updated_at'])),
            'days_since_update' => (int)$product['days_since_update'],
            'is_recently_updated' => $product['days_since_update'] <= 7,
            'track_quantity' => (bool)$product['track_quantity'],
            'allow_backorder' => (bool)$product['allow_backorder'],
            'is_featured' => (bool)$product['is_featured']
        ];
        
        // Get recent sales data for this product
        try {
            $sales_stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total_sales,
                    SUM(si.quantity) as total_quantity_sold,
                    SUM(si.subtotal) as total_revenue,
                    MAX(s.created_at) as last_sale_date
                FROM sale_items si
                JOIN sales s ON si.sale_id = s.id
                WHERE si.product_id = ? 
                AND s.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $sales_stmt->bind_param("i", $id);
            $sales_stmt->execute();
            $sales_result = $sales_stmt->get_result();
            $sales_data = $sales_result->fetch_assoc();
            
            $product['sales_analytics'] = [
                'total_sales_30days' => (int)$sales_data['total_sales'],
                'total_quantity_sold_30days' => (int)$sales_data['total_quantity_sold'],
                'total_revenue_30days' => (float)$sales_data['total_revenue'],
                'total_revenue_30days_formatted' => formatRupiah($sales_data['total_revenue']),
                'last_sale_date' => $sales_data['last_sale_date'],
                'last_sale_days_ago' => $sales_data['last_sale_date'] ? 
                    floor((time() - strtotime($sales_data['last_sale_date'])) / (60 * 60 * 24)) : null,
                'average_daily_sales' => $sales_data['total_quantity_sold'] ? 
                    round($sales_data['total_quantity_sold'] / 30, 2) : 0
            ];
            
            $sales_stmt->close();
        } catch (Exception $e) {
            error_log("Sales analytics error: " . $e->getMessage());
            $product['sales_analytics'] = null;
        }
        
        // Clean up computed fields that were used for calculation
        unset($product['days_since_update']);
        unset($product['profit_margin_percentage']);
        
        echo json_encode([
            'success' => true,
            'product' => $product,
            'timestamp' => date('c')
        ]);
        
    } else {
        echo json_encode([
            'error' => 'Product not found',
            'message' => 'Product with ID ' . $id . ' not found or inactive',
            'product_id' => $id,
            'timestamp' => date('c')
        ]);
    }
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    error_log("Get product error: " . $e->getMessage());
    
    echo json_encode([
        'error' => 'Database error',
        'message' => 'Failed to retrieve product information',
        'error_code' => 'DATABASE_ERROR',
        'product_id' => $id,
        'timestamp' => date('c')
    ]);
}
?>