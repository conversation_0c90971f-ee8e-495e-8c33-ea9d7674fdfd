<?php
// ajax/search_product.php
require_once '../config/database.php';

header('Content-Type: application/json');

$barcode = $_GET['barcode'] ?? '';

if (!empty($barcode)) {
    $conn = connectDB();
    $barcode = escape($conn, $barcode);
    
    // Search by barcode or code
    $sql = "SELECT * FROM products WHERE barcode = '$barcode' OR code = '$barcode' LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        $product = $result->fetch_assoc();
        echo json_encode(['success' => true, 'product' => $product]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Product not found']);
    }
    
    $conn->close();
} else {
    echo json_encode(['success' => false, 'message' => 'No barcode provided']);
}
?>