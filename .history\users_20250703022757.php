<?php
require_once 'config/database.php';
checkAdmin();

$conn = connectDB();
$message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add') {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $full_name = $_POST['full_name'];
        $role = $_POST['role'];

        $stmt_check = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt_check->bind_param("s", $username);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            $message = '<div class="alert alert-danger">Username sudah digunakan!</div>';
        } else {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $username, $hashed_password, $full_name, $role);
            if ($stmt->execute()) $message = '<div class="alert alert-success">User berhasil ditambahkan!</div>';
            $stmt->close();
        }
        $stmt_check->close();
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        if ($id != $_SESSION['user_id']) {
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $id);
            if ($stmt->execute()) $message = '<div class="alert alert-success">User berhasil dihapus!</div>';
            $stmt->close();
        }
    }
}

$users = $conn->query("SELECT id, username, full_name, role, created_at FROM users ORDER BY full_name");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <title>Manajemen User - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            <div class="content">
                <?php echo $message; ?>
                <div class="card">
                     <div class="card-header"><h3>Daftar User</h3><button class="btn btn-primary" onclick="showAddModal()">Tambah</button></div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr><th>Username</th><th>Nama Lengkap</th><th>Role</th><th>Dibuat</th><th>Aksi</th></tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $users->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><span class="badge"><?php echo htmlspecialchars(ucfirst($user['role'])); ?></span></td>
                                    <td><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <form method="POST" onsubmit="return confirm('Yakin hapus?')"><input type="hidden" name="action" value="delete"><input type="hidden" name="id" value="<?php echo (int)$user['id']; ?>"><button type="submit"><i class="fas fa-trash"></i></button></form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
     </body>
</html>
<?php $conn->close(); ?>