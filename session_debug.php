<?php
// session_debug.php - Simple session debug tool
echo "<h1>Session Debug Tool</h1>";

echo "<h2>Step 1: Current Session Status</h2>";
echo "Session Status: " . session_status() . "<br>";
echo "PHP_SESSION_DISABLED (0): " . PHP_SESSION_DISABLED . "<br>";
echo "PHP_SESSION_NONE (1): " . PHP_SESSION_NONE . "<br>";
echo "PHP_SESSION_ACTIVE (2): " . PHP_SESSION_ACTIVE . "<br><br>";

if (session_status() == PHP_SESSION_NONE) {
    echo "<strong>Session NOT started yet. Starting now...</strong><br>";
    session_start();
    echo "Session started. New status: " . session_status() . "<br>";
} elseif (session_status() == PHP_SESSION_ACTIVE) {
    echo "<strong>✅ Session ALREADY ACTIVE</strong><br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "<strong>❌ Session DISABLED</strong><br>";
}

echo "<h2>Step 2: Include Database Config</h2>";
try {
    require_once 'config/database.php';
    echo "✅ Database config loaded successfully<br>";
    
    // Test database connection
    $conn = connectDB();
    echo "✅ Database connection successful<br>";
    $conn->close();
    
} catch (Exception $e) {
    echo "❌ Error loading database config: " . $e->getMessage() . "<br>";
}

echo "<h2>Step 3: Session Variables</h2>";
if (isset($_SESSION) && count($_SESSION) > 0) {
    echo "Session variables found:<br>";
    foreach ($_SESSION as $key => $value) {
        echo "- $key: " . (is_array($value) ? 'Array' : $value) . "<br>";
    }
} else {
    echo "No session variables set<br>";
    echo "<strong>Setting test session variables...</strong><br>";
    $_SESSION['test_user_id'] = 1;
    $_SESSION['test_role'] = 'admin';
    echo "Test variables set: user_id=1, role=admin<br>";
}

echo "<h2>Step 4: Function Tests</h2>";
$functions_to_test = [
    'connectDB',
    'escape', 
    'formatRupiah',
    'generateInvoiceNo',
    'checkLogin',
    'checkAdmin',
    'isLoggedIn',
    'isAdmin'
];

foreach ($functions_to_test as $func) {
    if (function_exists($func)) {
        echo "✅ Function $func() exists<br>";
    } else {
        echo "❌ Function $func() NOT found<br>";
    }
}

echo "<h2>Step 5: Test Session Helper Functions</h2>";
if (function_exists('isLoggedIn')) {
    $loggedIn = isLoggedIn();
    echo "isLoggedIn(): " . ($loggedIn ? 'TRUE' : 'FALSE') . "<br>";
} else {
    echo "isLoggedIn() function not available<br>";
}

if (function_exists('isAdmin')) {
    $isAdmin = isAdmin();
    echo "isAdmin(): " . ($isAdmin ? 'TRUE' : 'FALSE') . "<br>";
} else {
    echo "isAdmin() function not available<br>";
}

if (function_exists('getCurrentUser')) {
    $currentUser = getCurrentUser();
    echo "getCurrentUser(): " . print_r($currentUser, true) . "<br>";
} else {
    echo "getCurrentUser() function not available<br>";
}

echo "<h2>Step 6: File Check</h2>";
$files_to_check = [
    'config/database.php',
    'includes/functions.php',
    'sales.php',
    'products.php',
    'ajax/process_sale.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file NOT found<br>";
    }
}

echo "<h2>Step 7: Quick Fix Recommendations</h2>";

if (session_status() != PHP_SESSION_ACTIVE) {
    echo "🔧 Session not active. Add session check to your files.<br>";
}

echo "<h3>Recommended file header pattern:</h3>";
echo "<pre>";
echo htmlspecialchars('<?php
// For main pages
require_once \'config/database.php\';
checkLogin(); // or checkAdmin() for admin pages

// For AJAX files  
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once \'../config/database.php\';');
echo "</pre>";

echo "<h3>Common Issues:</h3>";
echo "<ul>";
echo "<li>Multiple session_start() calls → Use session_status() check</li>";
echo "<li>Missing session in AJAX → Include session check</li>";
echo "<li>Functions not found → Check includes/functions.php path</li>";
echo "<li>Database not connected → Check XAMPP MySQL running</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li><a href='system_status.php' style='color: #007cba;'>Check System Status</a></li>";
echo "<li><a href='setup_sample_data.php' style='color: #28a745;'>Setup Sample Data</a></li>";
echo "<li><a href='sales.php' style='color: #17a2b8;'>Test Sales Page</a></li>";
echo "</ol>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }";
echo "h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }";
echo "h2 { color: #666; margin-top: 25px; border-left: 4px solid #007cba; padding-left: 10px; }";
echo "pre { background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; }";
echo "ul, ol { margin-left: 20px; }";
echo "a { text-decoration: none; font-weight: bold; }";
echo "a:hover { text-decoration: underline; }";
echo "</style>";
?>