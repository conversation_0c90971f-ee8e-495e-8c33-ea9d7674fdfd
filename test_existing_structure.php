<?php
// test_existing_structure.php - Improved version
session_start();

// Simulate logged in user for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

echo "<h2>K5N System Testing - Improved Version</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $conn = connectDB();
    echo "✅ Database connection: SUCCESS<br>";
    
    // Test basic query
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Users table accessible: $count users found<br>";
    } else {
        echo "❌ Error querying users table: " . $conn->error . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "<br>";
}

// Test 2: Functions Test
echo "<h3>2. Functions Test</h3>";
try {
    // Test formatRupiah
    $formatted = formatRupiah(35000);
    echo "✅ formatRupiah(35000): $formatted<br>";
    
    // Test generateInvoiceNo
    if (function_exists('generateInvoiceNo')) {
        $invoice = generateInvoiceNo($conn);
        echo "✅ generateInvoiceNo(): $invoice<br>";
    } else {
        echo "❌ generateInvoiceNo function not found<br>";
    }
    
    // Test escape function
    if (function_exists('escape')) {
        $escaped = escape($conn, "test'string");
        echo "✅ escape function: works<br>";
    } else {
        echo "❌ escape function not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Functions test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 3: Include Functions File
echo "<h3>3. Include Test</h3>";
try {
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo "✅ includes/functions.php: LOADED<br>";
        
        // Test if getSalesSummary exists (from your existing functions)
        if (function_exists('getSalesSummary')) {
            echo "✅ getSalesSummary function: EXISTS<br>";
        } else {
            echo "❌ getSalesSummary function: NOT FOUND<br>";
        }
        
    } else {
        echo "❌ includes/functions.php: FILE NOT FOUND<br>";
    }
} catch (Exception $e) {
    echo "❌ Include test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 4: Check Database Tables
echo "<h3>4. Database Tables Check</h3>";
try {
    $tables = ['users', 'products', 'categories', 'sales', 'sale_items', 'stock_history'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "✅ Table '$table': EXISTS ($count records)<br>";
        } else {
            echo "❌ Table '$table': NOT FOUND<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Table check: FAILED - " . $e->getMessage() . "<br>";
}

// Test 5: Products with Stock - IMPROVED
echo "<h3>5. Products Test</h3>";
$testProduct = null;
try {
    $products_result = $conn->query("SELECT * FROM products WHERE stock > 0 ORDER BY id ASC LIMIT 5");
    if ($products_result->num_rows > 0) {
        echo "✅ Products with stock found:<br>";
        $first = true;
        while ($product = $products_result->fetch_assoc()) {
            echo "&nbsp;&nbsp;- ID:{$product['id']} - {$product['name']} (Code: {$product['code']}, Stock: {$product['stock']}, Price: " . formatRupiah($product['selling_price']) . ")<br>";
            
            // Use first product for testing
            if ($first) {
                $testProduct = $product;
                $first = false;
            }
        }
    } else {
        echo "❌ No products with stock found<br>";
        
        // Try to create a test product
        echo "<strong>Creating test product...</strong><br>";
        $conn->query("INSERT INTO products (code, name, category_id, selling_price, purchase_price, stock, min_stock) 
                     VALUES ('TEST001', 'Test Product', 1, 35000, 25000, 10, 5)");
        
        if ($conn->affected_rows > 0) {
            $testProduct = [
                'id' => $conn->insert_id,
                'code' => 'TEST001',
                'name' => 'Test Product',
                'selling_price' => 35000,
                'stock' => 10
            ];
            echo "✅ Test product created: ID {$testProduct['id']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Products test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 6: Check Current Stock Levels
echo "<h3>6. Current Stock Analysis</h3>";
try {
    $stock_analysis = $conn->query("
        SELECT 
            COUNT(*) as total_products,
            SUM(CASE WHEN stock > 0 THEN 1 ELSE 0 END) as products_with_stock,
            SUM(CASE WHEN stock <= min_stock THEN 1 ELSE 0 END) as low_stock_products,
            AVG(stock) as avg_stock
        FROM products
    ");
    
    if ($stock_analysis) {
        $stats = $stock_analysis->fetch_assoc();
        echo "✅ Total products: {$stats['total_products']}<br>";
        echo "✅ Products with stock: {$stats['products_with_stock']}<br>";
        echo "✅ Low stock products: {$stats['low_stock_products']}<br>";
        echo "✅ Average stock: " . round($stats['avg_stock'], 2) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Stock analysis: FAILED - " . $e->getMessage() . "<br>";
}

// Test 7: AJAX Endpoint Test - IMPROVED
echo "<h3>7. AJAX Endpoint Test</h3>";
if ($testProduct) {
    echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0; border-left: 4px solid #4caf50;'>";
    echo "<strong>Test Product Found:</strong><br>";
    echo "ID: {$testProduct['id']}<br>";
    echo "Name: {$testProduct['name']}<br>";
    echo "Code: {$testProduct['code']}<br>";
    echo "Price: " . formatRupiah($testProduct['selling_price']) . "<br>";
    echo "Stock: {$testProduct['stock']}<br>";
    echo "</div>";
    
    echo "<button onclick='testProcessSale()' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; margin-right: 10px;'>Test Process Sale</button>";
    echo "<button onclick='testAddProduct()' style='padding: 10px 20px; background: #28a745; color: white; border: none; cursor: pointer;'>Test Add to Cart</button>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107;'>";
    echo "<strong>⚠️ No products available for testing</strong><br>";
    echo "Please add some products first or check your database.";
    echo "</div>";
}

echo "<div id='ajaxResult' style='margin-top: 10px; padding: 10px; background: #f0f0f0; display: none;'></div>";

// Close connection
if (isset($conn)) {
    $conn->close();
}
?>

<script>
// Store test product data
const testProduct = <?php echo $testProduct ? json_encode($testProduct) : 'null'; ?>;

function testProcessSale() {
    if (!testProduct) {
        alert('No test product available');
        return;
    }
    
    // Show result area
    document.getElementById('ajaxResult').style.display = 'block';
    document.getElementById('ajaxResult').innerHTML = 'Testing process sale...';
    
    const testData = {
        items: [
            {
                id: testProduct.id,
                name: testProduct.name,
                code: testProduct.code, 
                price: parseFloat(testProduct.selling_price),
                quantity: 1
            }
        ],
        total: parseFloat(testProduct.selling_price),
        payment_method: 'cash',
        payment_received: parseFloat(testProduct.selling_price),
        change_amount: 0
    };
    
    console.log('Testing process sale with data:', testData);
    
    fetch('ajax/process_sale.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            console.log('Parsed JSON:', data);
            
            if (data.success) {
                document.getElementById('ajaxResult').innerHTML = 
                    '<div style="color: #28a745; font-weight: bold;">✅ PROCESS SALE SUCCESS!</div>' +
                    '<strong>Invoice:</strong> ' + data.invoice_no + '<br>' +
                    '<strong>Sale ID:</strong> ' + data.sale_id + '<br>' +
                    '<strong>Total:</strong> Rp ' + data.total.toLocaleString('id-ID') + '<br>' +
                    '<strong>Items:</strong> ' + data.items_count + '<br>' +
                    '<strong>Message:</strong> ' + data.message;
                    
                // Test receipt print
                setTimeout(() => {
                    if (confirm('Test print receipt?')) {
                        window.open('print_receipt.php?id=' + data.sale_id, '_blank');
                    }
                }, 1000);
            } else {
                document.getElementById('ajaxResult').innerHTML = 
                    '<div style="color: #dc3545; font-weight: bold;">❌ PROCESS SALE ERROR:</div>' + 
                    '<strong>Message:</strong> ' + data.message;
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            document.getElementById('ajaxResult').innerHTML = 
                '<div style="color: #dc3545; font-weight: bold;">❌ JSON Parse Error:</div>' +
                'Raw response: <pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;">' + 
                text.substring(0, 500) + '</pre>';
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        document.getElementById('ajaxResult').innerHTML = 
            '<div style="color: #dc3545; font-weight: bold;">❌ Network Error:</div>' + 
            error.message;
    });
}

function testAddProduct() {
    if (!testProduct) {
        alert('No test product available');
        return;
    }
    
    document.getElementById('ajaxResult').style.display = 'block';
    document.getElementById('ajaxResult').innerHTML = 
        '<div style="color: #17a2b8; font-weight: bold;">🛒 ADD TO CART TEST</div>' +
        '<strong>Product:</strong> ' + testProduct.name + '<br>' +
        '<strong>Code:</strong> ' + testProduct.code + '<br>' +
        '<strong>Price:</strong> Rp ' + parseFloat(testProduct.selling_price).toLocaleString('id-ID') + '<br>' +
        '<div style="margin-top: 10px; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb;">' +
        'This simulates adding product to cart in sales.php<br>' +
        'Product data is ready for sales system!' +
        '</div>';
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
h3 { color: #666; margin-top: 20px; border-left: 4px solid #007cba; padding-left: 10px; }
button { border-radius: 4px; font-size: 14px; }
button:hover { opacity: 0.9; }
pre { background: #f5f5f5; padding: 10px; border-left: 3px solid #007cba; font-size: 12px; }
.success { color: #28a745; }
.error { color: #dc3545; }
.warning { color: #ffc107; }
</style>