<?php
// config/database.php - Enhanced Database Configuration
// Replace your existing config/database.php with this complete file

define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', ''); // Kosong untuk XAMPP default
define('DB_NAME', 'k5n_inventory');

// Database connection with enhanced error handling
function connectDB() {
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($conn->connect_error) {
            error_log("Database connection failed: " . $conn->connect_error);
            throw new Exception("Database connection failed");
        }
        
        // Set charset and timezone
        $conn->set_charset("utf8mb4");
        $conn->query("SET time_zone = '+00:00'");
        
        return $conn;
    } catch (Exception $e) {
        error_log("Database error: " . $e->getMessage());
        die("Database connection error. Please check configuration.");
    }
}

// Enhanced escape function with validation
function escape($conn, $str) {
    if ($str === null) return null;
    return $conn->real_escape_string(trim($str));
}

// Set current user for audit trail
function setCurrentUser($user_id) {
    try {
        $conn = connectDB();
        $conn->query("SET @current_user_id = $user_id");
        $conn->close();
    } catch (Exception $e) {
        error_log("Failed to set current user: " . $e->getMessage());
    }
}

// Session management with enhanced security
if (session_status() == PHP_SESSION_NONE) {
    session_start();
    
    // Regenerate session ID for security
    if (!isset($_SESSION['regenerated'])) {
        session_regenerate_id(true);
        $_SESSION['regenerated'] = true;
    }
    
    // Set current user for audit trail
    if (isset($_SESSION['user_id'])) {
        setCurrentUser($_SESSION['user_id']);
    }
}

// Enhanced authentication functions
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header("Location: index.php");
        exit();
    }
    
    // Update last activity
    updateLastActivity($_SESSION['user_id']);
}

function checkAdmin() {
    checkLogin();
    if (!in_array($_SESSION['role'], ['admin', 'manager'])) {
        $_SESSION['error'] = "Access denied! Admin or Manager role required.";
        header("Location: dashboard.php");
        exit();
    }
}

function updateLastActivity($user_id) {
    try {
        $conn = connectDB();
        $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $conn->close();
    } catch (Exception $e) {
        error_log("Failed to update last activity: " . $e->getMessage());
    }
}

// Enhanced helper functions
function formatRupiah($amount) {
    return "Rp " . number_format($amount, 0, ',', '.');
}

function generateInvoiceNo($conn) {
    try {
        $result = $conn->query("SELECT generate_invoice_no() as invoice_no");
        if ($result) {
            $row = $result->fetch_assoc();
            return $row['invoice_no'];
        }
        
        // Fallback if function doesn't exist
        $today = date('Ymd');
        $result = $conn->query("SELECT COUNT(*) + 1 as next_no FROM sales WHERE DATE(created_at) = CURDATE()");
        $next_no = $result->fetch_assoc()['next_no'];
        return 'INV' . $today . str_pad($next_no, 4, '0', STR_PAD_LEFT);
        
    } catch (Exception $e) {
        error_log("Failed to generate invoice number: " . $e->getMessage());
        return 'INV' . date('YmdHis');
    }
}

function generateProductCode($conn, $category_code) {
    try {
        $category_prefix = strtoupper(substr($category_code, 0, 3));
        
        // Try to use function if exists
        $stmt = $conn->prepare("SELECT generate_product_code(?) as product_code");
        if ($stmt) {
            $stmt->bind_param("s", $category_prefix);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                return $row['product_code'];
            }
        }
        
        // Fallback method
        $result = $conn->query("SELECT COUNT(*) + 1 as next_no FROM products WHERE code LIKE '$category_prefix%'");
        $next_no = $result->fetch_assoc()['next_no'];
        return $category_prefix . str_pad($next_no, 5, '0', STR_PAD_LEFT);
        
    } catch (Exception $e) {
        error_log("Failed to generate product code: " . $e->getMessage());
        return 'PRD' . date('His');
    }
}

// System settings functions
function getSystemSetting($key, $default = null) {
    try {
        $conn = connectDB();
        $stmt = $conn->prepare("SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $value = $row['setting_value'];
            
            // Convert based on type
            switch ($row['setting_type']) {
                case 'number':
                    return (float)$value;
                case 'boolean':
                    return filter_var($value, FILTER_VALIDATE_BOOLEAN);
                case 'json':
                    return json_decode($value, true);
                default:
                    return $value;
            }
        }
        
        $conn->close();
        return $default;
    } catch (Exception $e) {
        error_log("Failed to get system setting: " . $e->getMessage());
        return $default;
    }
}

function setSystemSetting($key, $value, $type = 'string') {
    try {
        $conn = connectDB();
        
        // Convert value based on type
        switch ($type) {
            case 'json':
                $value = json_encode($value);
                break;
            case 'boolean':
                $value = $value ? 'true' : 'false';
                break;
            default:
                $value = (string)$value;
        }
        
        $stmt = $conn->prepare("
            INSERT INTO system_settings (setting_key, setting_value, setting_type) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            setting_type = VALUES(setting_type),
            updated_at = NOW()
        ");
        $stmt->bind_param("sss", $key, $value, $type);
        $result = $stmt->execute();
        $conn->close();
        return $result;
    } catch (Exception $e) {
        error_log("Failed to set system setting: " . $e->getMessage());
        return false;
    }
}

// Logging functions
function logActivity($message, $context = [], $level = 'INFO') {
    try {
        $conn = connectDB();
        $user_id = $_SESSION['user_id'] ?? null;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $context_json = json_encode($context);
        
        $stmt = $conn->prepare("
            INSERT INTO system_logs (level, message, context, user_id, ip_address) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("sssis", $level, $message, $context_json, $user_id, $ip_address);
        $stmt->execute();
        $conn->close();
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Helper function untuk cek session status
function isSessionActive() {
    return session_status() === PHP_SESSION_ACTIVE;
}

// Helper function untuk get current user info
function getCurrentUser() {
    if (!isSessionActive()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'role' => $_SESSION['role'] ?? null,
        'full_name' => $_SESSION['full_name'] ?? null
    ];
}

// Helper function untuk cek apakah user sudah login
function isLoggedIn() {
    return isSessionActive() && isset($_SESSION['user_id']);
}

// Helper function untuk cek apakah user adalah admin
function isAdmin() {
    return isLoggedIn() && in_array($_SESSION['role'], ['admin', 'manager']);
}

// Additional helper functions
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
?>