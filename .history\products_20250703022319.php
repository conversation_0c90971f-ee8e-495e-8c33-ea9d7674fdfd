<?php
// products.php (VERSI AMAN)
require_once 'config/database.php';
checkAdmin(); // <PERSON>ya admin yang bisa akses

$conn = connectDB();
$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    // --- AKSI TAMBAH PRODUK ---
    if ($action == 'add') {
        $name = $_POST['name'];
        $category_id = (int)$_POST['category_id'];
        $purchase_price = (float)$_POST['purchase_price'];
        $selling_price = (float)$_POST['selling_price'];
        $stock = (int)$_POST['stock'];
        $min_stock = (int)$_POST['min_stock'];
        $description = $_POST['description'];
        
        // Validasi Sederhana
        if (empty($name) || $selling_price <= 0) {
            $message = '<div class="alert alert-danger"><PERSON><PERSON> dan <PERSON>al tidak boleh kosong!</div>';
        } else {
            // Generate product code (asumsi fungsi ini sudah diamankan di database.php)
            $cat_stmt = $conn->prepare("SELECT name FROM categories WHERE id = ?");
            $cat_stmt->bind_param("i", $category_id);
            $cat_stmt->execute();
            $cat_result = $cat_stmt->get_result();
            $cat_name = $cat_result->fetch_assoc()['name'] ?? 'GEN';
            $cat_stmt->close();

            $code = generateProductCode($conn, $cat_name);
            $barcode = $code; // Barcode disamakan dengan Kode Produk

            $stmt = $conn->prepare("INSERT INTO products (code, name, category_id, barcode, purchase_price, selling_price, stock, min_stock, description) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssisddiis", $code, $name, $category_id, $barcode, $purchase_price, $selling_price, $stock, $min_stock, $description);

            if ($stmt->execute()) {
                $message = '<div class="alert alert-success">Produk berhasil ditambahkan!</div>';
            } else {
                $message = '<div class="alert alert-danger">Error: ' . $stmt->error . '</div>';
            }
            $stmt->close();
        }
    }
    
    // --- AKSI HAPUS PRODUK ---
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            $message = '<div class="alert alert-success">Produk berhasil dihapus!</div>';
        } else {
             $message = '<div class="alert alert-danger">Error: ' . $stmt->error . '</div>';
        }
        $stmt->close();
    }
}

// Get all products (query ini tidak ada input user, jadi relatif aman)
$products = $conn->query("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    ORDER BY p.name
");

// Get categories for dropdown
$categories = $conn->query("SELECT * FROM categories ORDER BY name");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Barang - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Data Barang</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Data Barang</span>
                    </div>
                </div>
                
                <?php echo $message; // Pesan sudah berupa HTML, jadi tidak perlu escape ?>
                
                <div class="card">
                    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Daftar Barang</h3>
                        <button class="btn btn-primary" onclick="showAddModal()">
                            <i class="fas fa-plus"></i> Tambah Barang
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Kode</th>
                                        <th>Nama Barang</th>
                                        <th>Kategori</th>
                                        <th>Harga Jual</th>
                                        <th>Stok</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($row = $products->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['code']); ?></td>
                                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['category_name']); ?></td>
                                        <td><?php echo formatRupiah($row['selling_price']); ?></td>
                                        <td>
                                            <?php if ($row['stock'] <= $row['min_stock']): ?>
                                                <span style="color: var(--danger-color); font-weight: bold;">
                                                    <?php echo (int)$row['stock']; ?>
                                                </span>
                                            <?php else: ?>
                                                <?php echo (int)$row['stock']; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm" onclick="printBarcode('<?php echo htmlspecialchars($row['id'], ENT_QUOTES, 'UTF-8'); ?>')" 
                                                    style="background-color: var(--primary-color); color: white; padding: 0.25rem 0.5rem;">
                                                <i class="fas fa-barcode"></i>
                                            </button>
                                            <button class="btn btn-sm" onclick="editProduct(<?php echo htmlspecialchars(json_encode($row), ENT_QUOTES, 'UTF-8'); ?>)" 
                                                    style="background-color: var(--warning-color); color: white; padding: 0.25rem 0.5rem;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Yakin hapus produk ini?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo (int)$row['id']; ?>">
                                                <button type="submit" class="btn btn-sm" 
                                                        style="background-color: var(--danger-color); color: white; padding: 0.25rem 0.5rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    </body>
</html>
<?php $conn->close(); ?>