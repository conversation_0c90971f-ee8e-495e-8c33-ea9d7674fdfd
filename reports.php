<?php
// reports.php - <PERSON><PERSON><PERSON>
require_once 'config/database.php';
checkLogin();

$conn = connectDB();

// Default date range (this month)
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-t');
$report_type = $_GET['type'] ?? 'daily';

// Get report data based on type
$report_data = [];
$summary = [];

// Summary statistics
$sql = "SELECT 
            COUNT(DISTINCT s.id) as total_transactions,
            SUM(s.total_amount) as total_revenue,
            SUM(si.quantity) as total_items,
            AVG(s.total_amount) as avg_transaction
        FROM sales s
        JOIN sale_items si ON s.id = si.sale_id
        WHERE DATE(s.created_at) BETWEEN '$start_date' AND '$end_date'";

$result = $conn->query($sql);
$summary = $result->fetch_assoc();

// Detailed report based on type
switch ($report_type) {
    case 'daily':
        $sql = "SELECT 
                    DATE(s.created_at) as sale_date,
                    COUNT(DISTINCT s.id) as transactions,
                    SUM(s.total_amount) as revenue,
                    SUM(si.quantity) as items_sold
                FROM sales s
                JOIN sale_items si ON s.id = si.sale_id
                WHERE DATE(s.created_at) BETWEEN '$start_date' AND '$end_date'
                GROUP BY DATE(s.created_at)
                ORDER BY sale_date DESC";
        break;
        
    case 'product':
        $sql = "SELECT 
                    p.code,
                    p.name,
                    SUM(si.quantity) as quantity_sold,
                    SUM(si.subtotal) as total_revenue,
                    COUNT(DISTINCT si.sale_id) as times_sold
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                JOIN sales s ON si.sale_id = s.id
                WHERE DATE(s.created_at) BETWEEN '$start_date' AND '$end_date'
                GROUP BY p.id
                ORDER BY quantity_sold DESC";
        break;
        
    case 'cashier':
        $sql = "SELECT 
                    u.full_name as cashier,
                    COUNT(s.id) as transactions,
                    SUM(s.total_amount) as revenue,
                    AVG(s.total_amount) as avg_transaction
                FROM sales s
                JOIN users u ON s.user_id = u.id
                WHERE DATE(s.created_at) BETWEEN '$start_date' AND '$end_date'
                GROUP BY u.id
                ORDER BY revenue DESC";
        break;
}

$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) {
    $report_data[] = $row;
}

// Handle Excel export
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="laporan_' . $report_type . '_' . date('Ymd') . '.xls"');
    
    // Generate Excel content
    echo '<table border="1">';
    echo '<tr><th colspan="5">Laporan Penjualan K5N Apps</th></tr>';
    echo '<tr><th colspan="5">Periode: ' . date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)) . '</th></tr>';
    echo '<tr><td colspan="5"></td></tr>';
    
    // Headers based on report type
    echo '<tr>';
    if ($report_type == 'daily') {
        echo '<th>Tanggal</th><th>Jumlah Transaksi</th><th>Total Penjualan</th><th>Item Terjual</th>';
    } elseif ($report_type == 'product') {
        echo '<th>Kode</th><th>Nama Produk</th><th>Qty Terjual</th><th>Total Revenue</th><th>Frekuensi</th>';
    } elseif ($report_type == 'cashier') {
        echo '<th>Kasir</th><th>Transaksi</th><th>Total Penjualan</th><th>Rata-rata</th>';
    }
    echo '</tr>';
    
    // Data rows
    foreach ($report_data as $row) {
        echo '<tr>';
        foreach ($row as $value) {
            echo '<td>' . $value . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Laporan Penjualan</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Laporan</span>
                    </div>
                </div>
                
                <!-- Filter Form -->
                <div class="card" style="margin-bottom: 1.5rem;">
                    <div class="card-body">
                        <form method="GET" style="display: flex; gap: 1rem; align-items: end;">
                            <div class="form-group" style="margin: 0;">
                                <label class="form-label">Jenis Laporan</label>
                                <select name="type" class="form-control">
                                    <option value="daily" <?php echo $report_type == 'daily' ? 'selected' : ''; ?>>Harian</option>
                                    <option value="product" <?php echo $report_type == 'product' ? 'selected' : ''; ?>>Per Produk</option>
                                    <option value="cashier" <?php echo $report_type == 'cashier' ? 'selected' : ''; ?>>Per Kasir</option>
                                </select>
                            </div>
                            
                            <div class="form-group" style="margin: 0;">
                                <label class="form-label">Tanggal Mulai</label>
                                <input type="date" name="start_date" class="form-control" value="<?php echo $start_date; ?>">
                            </div>
                            
                            <div class="form-group" style="margin: 0;">
                                <label class="form-label">Tanggal Akhir</label>
                                <input type="date" name="end_date" class="form-control" value="<?php echo $end_date; ?>">
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Tampilkan
                            </button>
                            
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'excel'])); ?>" 
                               class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </a>
                        </form>
                    </div>
                </div>
                
                <!-- Summary Cards -->
                <div class="stats-grid" style="margin-bottom: 2rem;">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo number_format($summary['total_transactions']); ?></h3>
                            <p>Total Transaksi</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo formatRupiah($summary['total_revenue']); ?></h3>
                            <p>Total Penjualan</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo number_format($summary['total_items']); ?></h3>
                            <p>Item Terjual</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon bg-danger">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-details">
                            <h3><?php echo formatRupiah($summary['avg_transaction']); ?></h3>
                            <p>Rata-rata/Transaksi</p>
                        </div>
                    </div>
                </div>
                
                <!-- Report Chart -->
                <div class="card" style="margin-bottom: 1.5rem;">
                    <div class="card-header">
                        <h3>Grafik <?php echo ucfirst($report_type); ?></h3>
                    </div>
                    <div class="card-body">
                        <canvas id="reportChart" height="100"></canvas>
                    </div>
                </div>
                
                <!-- Report Table -->
                <div class="card">
                    <div class="card-header">
                        <h3>Detail Laporan</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <?php if ($report_type == 'daily'): ?>
                                            <th>Tanggal</th>
                                            <th>Jumlah Transaksi</th>
                                            <th>Total Penjualan</th>
                                            <th>Item Terjual</th>
                                        <?php elseif ($report_type == 'product'): ?>
                                            <th>Kode</th>
                                            <th>Nama Produk</th>
                                            <th>Qty Terjual</th>
                                            <th>Total Revenue</th>
                                            <th>Frekuensi</th>
                                        <?php elseif ($report_type == 'cashier'): ?>
                                            <th>Kasir</th>
                                            <th>Transaksi</th>
                                            <th>Total Penjualan</th>
                                            <th>Rata-rata</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <?php if ($report_type == 'daily'): ?>
                                            <td><?php echo date('d/m/Y', strtotime($row['sale_date'])); ?></td>
                                            <td><?php echo number_format($row['transactions']); ?></td>
                                            <td><?php echo formatRupiah($row['revenue']); ?></td>
                                            <td><?php echo number_format($row['items_sold']); ?></td>
                                        <?php elseif ($report_type == 'product'): ?>
                                            <td><?php echo $row['code']; ?></td>
                                            <td><?php echo $row['name']; ?></td>
                                            <td><?php echo number_format($row['quantity_sold']); ?></td>
                                            <td><?php echo formatRupiah($row['total_revenue']); ?></td>
                                            <td><?php echo number_format($row['times_sold']); ?>x</td>
                                        <?php elseif ($report_type == 'cashier'): ?>
                                            <td><?php echo $row['cashier']; ?></td>
                                            <td><?php echo number_format($row['transactions']); ?></td>
                                            <td><?php echo formatRupiah($row['revenue']); ?></td>
                                            <td><?php echo formatRupiah($row['avg_transaction']); ?></td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Report Chart
        const ctx = document.getElementById('reportChart').getContext('2d');
        const reportData = <?php echo json_encode($report_data); ?>;
        const reportType = '<?php echo $report_type; ?>';
        
        let labels = [];
        let data = [];
        
        if (reportType === 'daily') {
            labels = reportData.map(item => new Date(item.sale_date).toLocaleDateString('id-ID'));
            data = reportData.map(item => item.revenue);
        } else if (reportType === 'product') {
            labels = reportData.slice(0, 10).map(item => item.name);
            data = reportData.slice(0, 10).map(item => item.total_revenue);
        } else if (reportType === 'cashier') {
            labels = reportData.map(item => item.cashier);
            data = reportData.map(item => item.revenue);
        }
        
        new Chart(ctx, {
            type: reportType === 'product' ? 'bar' : 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Penjualan (Rp)',
                    data: data,
                    borderColor: '#2563eb',
                    backgroundColor: reportType === 'product' ? '#2563eb' : 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
<?php $conn->close(); ?>