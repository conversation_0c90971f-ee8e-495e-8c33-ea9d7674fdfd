<?php
// config/database.php - Complete Database Configuration

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include helper functions
require_once __DIR__ . 'includes/functions.php';

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'k5n_inventory');

/**
 * Database connection function
 */
function connectDB() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset to UTF-8
    $conn->set_charset("utf8");
    
    return $conn;
}

/**
 * Test database connection
 */
function testDBConnection() {
    try {
        $conn = connectDB();
        $conn->close();
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Application settings
define('APP_NAME', 'K5N Inventory System');
define('APP_VERSION', '1.0');
define('DEFAULT_TIMEZONE', 'Asia/Jakarta');

// Set timezone
date_default_timezone_set(DEFAULT_TIMEZONE);

// Error reporting for development (set to 0 for production)
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Security settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);

// Additional helper functions specific to this application

/**
 * Debug function
 */
function debug($data) {
    echo '<pre>';
    print_r($data);
    echo '</pre>';
}

/**
 * Redirect function
 */
function redirect($url) {
    header('Location: ' . $url);
    exit();
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user role
 */
function getCurrentUserRole() {
    return $_SESSION['role'] ?? null;
}

/**
 * Check if current user is admin
 */
function isAdmin() {
    return getCurrentUserRole() === 'admin';
}

/**
 * Get base URL
 */
function getBaseURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script);
    
    return $protocol . '://' . $host . $path;
}

/**
 * Create alert message
 */
function createAlert($type, $message) {
    return '<div class="alert alert-' . $type . '">' . $message . '</div>';
}

/**
 * Set flash message
 */
function setFlashMessage($type, $message) {
    $_SESSION['flash_' . $type] = $message;
}

/**
 * Get and clear flash message
 */
function getFlashMessage($type) {
    if (isset($_SESSION['flash_' . $type])) {
        $message = $_SESSION['flash_' . $type];
        unset($_SESSION['flash_' . $type]);
        return $message;
    }
    return null;
}

/**
 * Sanitize input
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Get configuration value
 */
function getConfig($key, $default = null) {
    $configs = [
        'app_name' => APP_NAME,
        'app_version' => APP_VERSION,
        'timezone' => DEFAULT_TIMEZONE,
        'currency' => 'IDR',
        'currency_symbol' => 'Rp',
        'date_format' => 'd/m/Y',
        'datetime_format' => 'd/m/Y H:i:s',
        'items_per_page' => 20
    ];
    
    return $configs[$key] ?? $default;
}

/**
 * Log error to file
 */
function logError($message, $file = null) {
    $logFile = $file ?? __DIR__ . '/../logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    
    // Create logs directory if it doesn't exist
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Send JSON response
 */
function sendJSONResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}

/**
 * Get pagination info
 */
function getPagination($currentPage, $totalRecords, $recordsPerPage = 20) {
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $offset = ($currentPage - 1) * $recordsPerPage;
    
    return [
        'current_page' => $currentPage,
        'total_pages' => $totalPages,
        'total_records' => $totalRecords,
        'records_per_page' => $recordsPerPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages
    ];
}

// Initialize error handling
set_error_handler(function($severity, $message, $file, $line) {
    logError("Error: [$severity] $message in $file on line $line");
});

set_exception_handler(function($exception) {
    logError("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
});
?>