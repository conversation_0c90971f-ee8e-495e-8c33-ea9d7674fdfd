<?php
// ajax/search_products.php - Live search for products in Enhanced Database
// Create this file in ajax/ folder

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
checkLogin();

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$query = $_GET['q'] ?? '';

if (strlen($query) < 2) {
    echo json_encode(['products' => []]);
    exit;
}

try {
    $conn = connectDB();
    
    // Prepare search term
    $search_term = "%$query%";
    $exact_term = "$query%";
    
    // Enhanced search query with relevance scoring
    $stmt = $conn->prepare("
        SELECT 
            p.id,
            p.code,
            p.barcode,
            p.name,
            p.selling_price,
            p.purchase_price,
            p.stock,
            p.min_stock,
            c.name as category_name,
            s.name as supplier_name,
            CASE 
                WHEN p.stock <= 0 THEN 'out_of_stock'
                WHEN p.stock <= p.min_stock THEN 'low_stock'
                ELSE 'normal'
            END as stock_status,
            -- Relevance scoring for better search results
            CASE 
                WHEN p.name LIKE ? THEN 1
                WHEN p.code LIKE ? THEN 2
                WHEN p.barcode LIKE ? THEN 3
                WHEN c.name LIKE ? THEN 4
                ELSE 5
            END as relevance_score
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE (
            p.name LIKE ? OR 
            p.code LIKE ? OR 
            p.barcode LIKE ? OR
            c.name LIKE ?
        ) 
        AND p.is_active = TRUE 
        ORDER BY 
            relevance_score ASC,
            p.stock DESC,
            p.name ASC
        LIMIT 10
    ");
    
    $stmt->bind_param("ssssssss", 
        $exact_term, $exact_term, $exact_term, $exact_term,
        $search_term, $search_term, $search_term, $search_term
    );
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $products = [];
    while ($row = $result->fetch_assoc()) {
        // Add computed fields
        $row['price_formatted'] = formatRupiah($row['selling_price']);
        $row['purchase_price_formatted'] = formatRupiah($row['purchase_price']);
        $row['profit_margin'] = $row['purchase_price'] > 0 ? 
            round((($row['selling_price'] - $row['purchase_price']) / $row['selling_price']) * 100, 2) : 0;
        
        // Add stock warning flags
        $row['is_low_stock'] = $row['stock'] <= $row['min_stock'];
        $row['is_out_of_stock'] = $row['stock'] <= 0;
        
        $products[] = $row;
    }
    
    echo json_encode([
        'products' => $products,
        'total_found' => count($products),
        'search_query' => $query,
        'timestamp' => date('c')
    ]);
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    error_log("Live search error: " . $e->getMessage());
    
    echo json_encode([
        'products' => [],
        'error' => 'Search failed',
        'message' => 'Terjadi kesalahan saat mencari produk',
        'timestamp' => date('c')
    ]);
}
?>