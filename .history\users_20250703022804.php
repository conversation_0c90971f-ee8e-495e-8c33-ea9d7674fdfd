<?php
// users.php - User Management
require_once 'config/database.php';
checkAdmin(); // Only admin can access

$conn = connectDB();
$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add') {
        $username = escape($conn, $_POST['username']);
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $full_name = escape($conn, $_POST['full_name']);
        $role = escape($conn, $_POST['role']);
        
        // Check if username exists
        $check = $conn->query("SELECT id FROM users WHERE username = '$username'");
        if ($check->num_rows > 0) {
            $message = '<div class="alert alert-danger">Username sudah digunakan!</div>';
        } else {
            $sql = "INSERT INTO users (username, password, full_name, role) 
                    VALUES ('$username', '$password', '$full_name', '$role')";
            
            if ($conn->query($sql)) {
                $message = '<div class="alert alert-success">User berhasil ditambahkan!</div>';
            } else {
                $message = '<div class="alert alert-danger">Error: ' . $conn->error . '</div>';
            }
        }
    }
    
    if ($action == 'edit') {
        $id = (int)$_POST['id'];
        $full_name = escape($conn, $_POST['full_name']);
        $role = escape($conn, $_POST['role']);
        
        $sql = "UPDATE users SET full_name = '$full_name', role = '$role' WHERE id = $id";
        
        // Update password if provided
        if (!empty($_POST['password'])) {
            $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $sql = "UPDATE users SET full_name = '$full_name', role = '$role', password = '$password' WHERE id = $id";
        }
        
        if ($conn->query($sql)) {
            $message = '<div class="alert alert-success">User berhasil diupdate!</div>';
        } else {
            $message = '<div class="alert alert-danger">Error: ' . $conn->error . '</div>';
        }
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        
        // Don't delete if it's the last admin
        $check = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $admin_count = $check->fetch_assoc()['count'];
        
        $user = $conn->query("SELECT role FROM users WHERE id = $id")->fetch_assoc();
        
        if ($user['role'] == 'admin' && $admin_count <= 1) {
            $message = '<div class="alert alert-danger">Tidak bisa menghapus admin terakhir!</div>';
        } else if ($id == $_SESSION['user_id']) {
            $message = '<div class="alert alert-danger">Tidak bisa menghapus akun sendiri!</div>';
        } else {
            $sql = "DELETE FROM users WHERE id = $id";
            if ($conn->query($sql)) {
                $message = '<div class="alert alert-success">User berhasil dihapus!</div>';
            }
        }
    }
}

// Get all users
$users = $conn->query("SELECT * FROM users ORDER BY full_name");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen User - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Manajemen User</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Manajemen User</span>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="card">
                    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Daftar User</h3>
                        <button class="btn btn-primary" onclick="showAddModal()">
                            <i class="fas fa-plus"></i> Tambah User
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Nama Lengkap</th>
                                        <th>Role</th>
                                        <th>Dibuat</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($user = $users->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $user['username']; ?></td>
                                        <td><?php echo $user['full_name']; ?></td>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $user['role'] == 'admin' ? 'var(--primary-color)' : 'var(--success-color)'; ?>; color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">
                                                <?php echo ucfirst($user['role']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></td>
                                        <td>
                                            <button class="btn btn-sm" onclick='editUser(<?php echo json_encode($user); ?>)' 
                                                    style="background-color: var(--warning-color); color: white; padding: 0.25rem 0.5rem;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Yakin hapus user ini?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-sm" 
                                                        style="background-color: var(--danger-color); color: white; padding: 0.25rem 0.5rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add User Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <h2>Tambah User Baru</h2>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                
                <div class="form-group">
                    <label class="form-label">Username</label>
                    <input type="text" name="username" class="form-control" required pattern="[a-zA-Z0-9]{3,20}" 
                           title="Username harus 3-20 karakter, hanya huruf dan angka">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password</label>
                    <input type="password" name="password" class="form-control" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Nama Lengkap</label>
                    <input type="text" name="full_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select name="role" class="form-control" required>
                        <option value="user">User (Kasir)</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeModal()" 
                            style="background-color: #6b7280; color: white;">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Edit User Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <h2>Edit User</h2>
            <form method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">
                
                <div class="form-group">
                    <label class="form-label">Username</label>
                    <input type="text" id="edit_username" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Password Baru (kosongkan jika tidak ingin mengubah)</label>
                    <input type="password" name="password" class="form-control" minlength="6">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Nama Lengkap</label>
                    <input type="text" name="full_name" id="edit_full_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select name="role" id="edit_role" class="form-control" required>
                        <option value="user">User (Kasir)</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeEditModal()" 
                            style="background-color: #6b7280; color: white;">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('addModal').style.display = 'none';
        }
        
        function editUser(user) {
            document.getElementById('edit_id').value = user.id;
            document.getElementById('edit_username').value = user.username;
            document.getElementById('edit_full_name').value = user.full_name;
            document.getElementById('edit_role').value = user.role;
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.className === 'modal') {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
<?php $conn->close(); ?>