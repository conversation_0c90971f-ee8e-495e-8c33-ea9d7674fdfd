<?php
// ajax/search_products.php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require_once '../config/database.php';

header('Content-Type: application/json');

$query = $_GET['q'] ?? '';

if (strlen($query) < 2) {
    echo json_encode(['products' => []]);
    exit;
}

$conn = connectDB();
$query = escape($conn, $query);

$sql = "SELECT id, code, name, selling_price, stock 
        FROM products 
        WHERE (name LIKE '%$query%' OR code LIKE '%$query%') 
        AND stock > 0 
        LIMIT 10";

$result = $conn->query($sql);
$products = [];

while ($row = $result->fetch_assoc()) {
    $products[] = $row;
}

echo json_encode(['products' => $products]);

$conn->close();
?>