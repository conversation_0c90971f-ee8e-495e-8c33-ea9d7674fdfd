<?php
// ajax/process_sale.php
require_once '../config/database.php';
session_start();

header('Content-Type: application/json');

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);

if (!empty($data['items']) && isset($data['total'])) {
    $conn = connectDB();
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate invoice number
        $invoice_no = generateInvoiceNo($conn);
        $user_id = $_SESSION['user_id'];
        $total = $data['total'];
        $payment_method = $data['payment_method'];
        $payment_received = $data['payment_received'];
        $change_amount = $data['change_amount'];
        
        // Insert sale
        $sql = "INSERT INTO sales (invoice_no, user_id, total_amount, payment_method, payment_received, change_amount) 
                VALUES ('$invoice_no', $user_id, $total, '$payment_method', $payment_received, $change_amount)";
        
        if (!$conn->query($sql)) {
            throw new Exception($conn->error);
        }
        
        $sale_id = $conn->insert_id;
        
        // Insert sale items
        foreach ($data['items'] as $item) {
            $product_id = $item['id'];
            $quantity = $item['quantity'];
            $price = $item['price'];
            $subtotal = $price * $quantity;
            
            $sql = "INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) 
                    VALUES ($sale_id, $product_id, $quantity, $price, $subtotal)";
            
            if (!$conn->query($sql)) {
                throw new Exception($conn->error);
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'invoice_no' => $invoice_no,
            'sale_id' => $sale_id
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    
    $conn->close();
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid data'
    ]);
}
?>