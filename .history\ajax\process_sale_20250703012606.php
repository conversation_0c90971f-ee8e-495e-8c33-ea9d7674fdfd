// ajax/process_sale.php (Versi Perbaikan dengan Prepared Statements)
<?php
// ajax/process_sale.php
require_once '../config/database.php'; // Pastikan path ini benar
session_start(); // Pastikan session sudah dimulai di sini atau di database.php (direkomendasikan di database.php)

header('Content-Type: application/json');

// Pastikan pengguna sudah login
// Ini penting, meskipun di database.php sudah ada checkLogin(), ini adalah endpoint AJAX
// jadi perlu cek lagi di sini agar tidak bisa diakses langsung tanpa login.
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Akses tidak sah. Silakan login kembali.'
    ]);
    exit;
}

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);

if (!empty($data['items']) && isset($data['total'])) {
    $conn = connectDB(); // Koneksi ke database
    
    // Mulai transaksi database
    // Ini memastikan semua operasi (insert penjualan, insert item, update stok) sukses semua atau gagal semua.
    $conn->begin_transaction();
    
    try {
        // Data dari input user (validasi dan type casting)
        $invoice_no = generateInvoiceNo($conn); // Asumsi generateInvoiceNo sudah aman dan butuh $conn
        $user_id = $_SESSION['user_id'];
        $total = (float)$data['total']; 
        $payment_method = htmlspecialchars($data['payment_method']); // Sanitasi tambahan untuk string
        $payment_received = (float)$data['payment_received'];
        $change_amount = (float)$data['change_amount'];
        
        // --- 1. Insert data penjualan (sales) ---
        // Membuat prepared statement untuk insert data penjualan
        // 's' = string, 'i' = integer, 'd' = double (float)
        $stmt_sale = $conn->prepare("INSERT INTO sales (invoice_no, user_id, total_amount, payment_method, payment_received, change_amount) 
                                    VALUES (?, ?, ?, ?, ?, ?)");
        if ($stmt_sale === false) {
            throw new Exception("Prepare statement for sales failed: " . $conn->error);
        }
        $stmt_sale->bind_param("sidddd", $invoice_no, $user_id, $total, $payment_method, $payment_received, $change_amount);
        
        if (!$stmt_sale->execute()) {
            throw new Exception("Gagal menyimpan data penjualan: " . $stmt_sale->error);
        }
        
        $sale_id = $stmt_sale->insert_id; // Dapatkan ID penjualan yang baru dibuat
        $stmt_sale->close(); // Tutup prepared statement

        // --- 2. Insert item-item penjualan (sale_items) dan Update Stok Produk ---
        // Membuat prepared statement untuk insert item penjualan
        $stmt_item = $conn->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) 
                                    VALUES (?, ?, ?, ?, ?)");
        if ($stmt_item === false) {
            throw new Exception("Prepare statement for sale items failed: " . $conn->error);
        }

        // Membuat prepared statement untuk update stok produk
        $stmt_update_stock = $conn->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
        if ($stmt_update_stock === false) {
            throw new Exception("Prepare statement for stock update failed: " . $conn->error);
        }
        
        foreach ($data['items'] as $item) {
            $product_id = (int)$item['id'];
            $quantity = (int)$item['quantity'];
            $price = (float)$item['price'];
            $subtotal = (float)$item['subtotal'];
            
            // Bind dan eksekusi untuk sale_items
            $stmt_item->bind_param("iiidd", $sale_id, $product_id, $quantity, $price, $subtotal);
            if (!$stmt_item->execute()) {
                throw new Exception("Gagal menyimpan item penjualan (Produk ID: {$product_id}): " . $stmt_item->error);
            }

            // Bind dan eksekusi untuk update stok
            $stmt_update_stock->bind_param("ii", $quantity, $product_id);
            if (!$stmt_update_stock->execute()) {
                throw new Exception("Gagal memperbarui stok (Produk ID: {$product_id}): " . $stmt_update_stock->error);
            }
        }
        $stmt_item->close(); // Tutup statement item
        $stmt_update_stock->close(); // Tutup statement update stok

        // --- 3. Log aktivitas (opsional tapi bagus) ---
        // Pastikan fungsi logActivity ada dan juga menggunakan prepared statement
        logActivity($conn, 'Melakukan transaksi penjualan dengan invoice: ' . $invoice_no, $sale_id);
        
        // Commit transaksi jika semua sukses
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'invoice_no' => $invoice_no,
            'sale_id' => $sale_id
        ]);
        
    } catch (Exception $e) {
        // Rollback transaksi jika ada yang gagal
        $conn->rollback();
        error_log("Process Sale Error: " . $e->getMessage()); // Log error ke server
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan saat memproses transaksi. Silakan coba lagi.' . 
                         (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] ? ' Detail: ' . $e->getMessage() : '') // Tampilkan detail hanya saat debug
        ]);
    }
    
    $conn->close(); // Tutup koneksi database
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Data transaksi tidak valid atau kosong.'
    ]);
}
?>