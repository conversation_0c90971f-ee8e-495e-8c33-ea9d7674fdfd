<?php
// products.php - Product Management
require_once 'config/database.php';
checkAdmin(); // Only admin can access

$conn = connectDB();
$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add') {
        $name = escape($conn, $_POST['name']);
        $category_id = (int)$_POST['category_id'];
        $purchase_price = (float)$_POST['purchase_price'];
        $selling_price = (float)$_POST['selling_price'];
        $stock = (int)$_POST['stock'];
        $min_stock = (int)$_POST['min_stock'];
        $description = escape($conn, $_POST['description']);
        
        // Generate product code
        $cat_result = $conn->query("SELECT name FROM categories WHERE id = $category_id");
        $cat_name = $cat_result->fetch_assoc()['name'];
        $code = generateProductCode($conn, $cat_name);
        
        // Generate barcode
        $barcode = 'K5N' . $code . rand(1000, 9999);
        
        $sql = "INSERT INTO products (code, name, category_id, barcode, purchase_price, selling_price, stock, min_stock, description) 
                VALUES ('$code', '$name', $category_id, '$barcode', $purchase_price, $selling_price, $stock, $min_stock, '$description')";
        
        if ($conn->query($sql)) {
            $message = '<div class="alert alert-success">Produk berhasil ditambahkan!</div>';
        } else {
            $message = '<div class="alert alert-danger">Error: ' . $conn->error . '</div>';
        }
    }
    
    if ($action == 'edit') {
        $id = (int)$_POST['id'];
        $name = escape($conn, $_POST['name']);
        $category_id = (int)$_POST['category_id'];
        $purchase_price = (float)$_POST['purchase_price'];
        $selling_price = (float)$_POST['selling_price'];
        $stock = (int)$_POST['stock'];
        $min_stock = (int)$_POST['min_stock'];
        $description = escape($conn, $_POST['description']);
        
        $sql = "UPDATE products SET 
                name = '$name',
                category_id = $category_id,
                purchase_price = $purchase_price,
                selling_price = $selling_price,
                stock = $stock,
                min_stock = $min_stock,
                description = '$description',
                updated_at = NOW()
                WHERE id = $id";
        
        if ($conn->query($sql)) {
            $message = '<div class="alert alert-success">Produk berhasil diupdate!</div>';
        } else {
            $message = '<div class="alert alert-danger">Error: ' . $conn->error . '</div>';
        }
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        $sql = "DELETE FROM products WHERE id = $id";
        
        if ($conn->query($sql)) {
            $message = '<div class="alert alert-success">Produk berhasil dihapus!</div>';
        }
    }
}

// Get all products
$products = $conn->query("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    ORDER BY p.name
");

// Get categories for dropdown
$categories = $conn->query("SELECT * FROM categories ORDER BY name");
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Barang - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Data Barang</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Data Barang</span>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="card">
                    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                        <h3>Daftar Barang</h3>
                        <button class="btn btn-primary" onclick="showAddModal()">
                            <i class="fas fa-plus"></i> Tambah Barang
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Kode</th>
                                        <th>Nama Barang</th>
                                        <th>Kategori</th>
                                        <th>Harga Beli</th>
                                        <th>Harga Jual</th>
                                        <th>Stok</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($row = $products->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $row['code']; ?></td>
                                        <td><?php echo $row['name']; ?></td>
                                        <td><?php echo $row['category_name']; ?></td>
                                        <td><?php echo formatRupiah($row['purchase_price']); ?></td>
                                        <td><?php echo formatRupiah($row['selling_price']); ?></td>
                                        <td>
                                            <?php if ($row['stock'] <= $row['min_stock']): ?>
                                                <span style="color: var(--danger-color); font-weight: bold;">
                                                    <?php echo $row['stock']; ?>
                                                </span>
                                            <?php else: ?>
                                                <?php echo $row['stock']; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm" onclick="printBarcode(<?php echo $row['id']; ?>)" 
                                                    style="background-color: var(--primary-color); color: white; padding: 0.25rem 0.5rem;">
                                                <i class="fas fa-barcode"></i>
                                            </button>
                                            <button class="btn btn-sm" onclick="editProduct(<?php echo $row['id']; ?>)" 
                                                    style="background-color: var(--warning-color); color: white; padding: 0.25rem 0.5rem;">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Yakin hapus produk ini?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                                <button type="submit" class="btn btn-sm" 
                                                        style="background-color: var(--danger-color); color: white; padding: 0.25rem 0.5rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Product Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <h2>Tambah Barang Baru</h2>
            <form method="POST">
                <input type="hidden" name="action" value="add">
                
                <div class="form-group">
                    <label class="form-label">Nama Barang</label>
                    <input type="text" name="name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Kategori</label>
                    <select name="category_id" class="form-control" required>
                        <option value="">-- Pilih Kategori --</option>
                        <?php 
                        $categories->data_seek(0);
                        while ($cat = $categories->fetch_assoc()): 
                        ?>
                        <option value="<?php echo $cat['id']; ?>"><?php echo $cat['name']; ?></option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Harga Beli</label>
                        <input type="number" name="purchase_price" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Harga Jual</label>
                        <input type="number" name="selling_price" class="form-control" required>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Stok Awal</label>
                        <input type="number" name="stock" class="form-control" value="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Minimal Stok</label>
                        <input type="number" name="min_stock" class="form-control" value="5" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Deskripsi</label>
                    <textarea name="description" class="form-control" rows="3"></textarea>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeModal()" 
                            style="background-color: #6b7280; color: white;">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Barcode Print Area -->
    <div id="printArea" class="print-area" style="display: none;"></div>
    
    <!-- Edit Product Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <h2>Edit Barang</h2>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">
                
                <div class="form-group">
                    <label class="form-label">Kode Barang</label>
                    <input type="text" id="edit_code" class="form-control" readonly>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Nama Barang</label>
                    <input type="text" name="name" id="edit_name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Kategori</label>
                    <select name="category_id" id="edit_category_id" class="form-control" required>
                        <option value="">-- Pilih Kategori --</option>
                        <?php 
                        $categories->data_seek(0);
                        while ($cat = $categories->fetch_assoc()): 
                        ?>
                        <option value="<?php echo $cat['id']; ?>"><?php echo $cat['name']; ?></option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Harga Beli</label>
                        <input type="number" name="purchase_price" id="edit_purchase_price" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Harga Jual</label>
                        <input type="number" name="selling_price" id="edit_selling_price" class="form-control" required>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label class="form-label">Stok</label>
                        <input type="number" name="stock" id="edit_stock" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Minimal Stok</label>
                        <input type="number" name="min_stock" id="edit_min_stock" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Deskripsi</label>
                    <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeEditModal()" 
                            style="background-color: #6b7280; color: white;">Batal</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        function showAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('addModal').style.display = 'none';
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        function editProduct(productId) {
            // Get product data via AJAX
            fetch('ajax/get_product.php?id=' + productId)
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        // Fill the edit form
                        document.getElementById('edit_id').value = data.id;
                        document.getElementById('edit_code').value = data.code;
                        document.getElementById('edit_name').value = data.name;
                        document.getElementById('edit_category_id').value = data.category_id || '';
                        document.getElementById('edit_purchase_price').value = data.purchase_price;
                        document.getElementById('edit_selling_price').value = data.selling_price;
                        document.getElementById('edit_stock').value = data.stock;
                        document.getElementById('edit_min_stock').value = data.min_stock;
                        document.getElementById('edit_description').value = data.description || '';
                        
                        // Show modal
                        document.getElementById('editModal').style.display = 'block';
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error loading product data: ' + error);
                });
        }
        
        function printBarcode(productId) {
            // Get product data via AJAX
            fetch('ajax/get_product.php?id=' + productId)
                .then(response => response.json())
                .then(data => {
                    const printArea = document.getElementById('printArea');
                    printArea.innerHTML = `
                        <div class="barcode-label">
                            <div class="product-name">${data.name}</div>
                            <svg id="barcode"></svg>
                            <div class="product-code">${data.barcode}</div>
                            <div class="product-price">${data.price_formatted}</div>
                        </div>
                    `;
                    
                    // Generate barcode
                    JsBarcode("#barcode", data.barcode, {
                        format: "CODE128",
                        width: 2,
                        height: 40,
                        displayValue: false
                    });
                    
                    // Print
                    window.print();
                });
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.className === 'modal') {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
<?php $conn->close(); ?>