<?php
// sales.php - Point of Sale
require_once 'config/database.php';
checkLogin();

$conn = connectDB();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penjualan - K5N Apps</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .pos-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 1.5rem;
            height: calc(100vh - 180px);
        }
        
        .product-search {
            margin-bottom: 1rem;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            overflow-y: auto;
            padding: 1rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
        }
        
        .product-item {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }
        
        .cart-container {
            background: white;
            border-radius: 0.5rem;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
        }
        
        .cart-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .cart-items {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .cart-footer {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background-color: var(--light-color);
        }
        
        .total-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            text-align: right;
            margin-bottom: 1rem;
        }
        
        .barcode-input {
            width: 100%;
            padding: 1rem;
            font-size: 1.2rem;
            border: 2px solid var(--primary-color);
            border-radius: 0.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include 'includes/navbar.php'; ?>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">Penjualan</h1>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span>/</span>
                        <span>Penjualan</span>
                    </div>
                </div>
                
                <div class="pos-container">
                    <!-- Product Selection -->
                    <div>
                        <div class="product-search">
                            <input type="text" 
                                   id="barcodeInput" 
                                   class="barcode-input" 
                                   placeholder="Scan barcode atau ketik kode produk..."
                                   autofocus>
                        </div>
                        
                        <div class="product-grid" id="productGrid">
                            <?php
                            $products = $conn->query("SELECT * FROM products WHERE stock > 0 ORDER BY name");
                            while ($product = $products->fetch_assoc()):
                            ?>
                            <div class="product-item" onclick="addToCart(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                <h4><?php echo $product['name']; ?></h4>
                                <p style="color: #6b7280; font-size: 0.875rem;"><?php echo $product['code']; ?></p>
                                <p style="font-weight: bold; color: var(--primary-color);">
                                    <?php echo formatRupiah($product['selling_price']); ?>
                                </p>
                                <p style="font-size: 0.75rem;">Stok: <?php echo $product['stock']; ?></p>
                            </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    
                    <!-- Cart -->
                    <div class="cart-container">
                        <div class="cart-header">
                            <h3>Keranjang Belanja</h3>
                        </div>
                        
                        <div class="cart-items" id="cartItems">
                            <p style="text-align: center; color: #6b7280;">Belum ada produk</p>
                        </div>
                        
                        <div class="cart-footer">
                            <div class="total-display">
                                Total: <span id="totalAmount">Rp 0</span>
                            </div>
                            
                            <button class="btn btn-success btn-block" onclick="processPayment()" id="payButton" disabled>
                                <i class="fas fa-cash-register"></i> Proses Pembayaran
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Modal -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <h2>Proses Pembayaran</h2>
            
            <div class="form-group">
                <label class="form-label">Total Belanja</label>
                <input type="text" id="modalTotal" class="form-control" readonly 
                       style="font-size: 1.5rem; font-weight: bold; text-align: right;">
            </div>
            
            <div class="form-group">
                <label class="form-label">Metode Pembayaran</label>
                <select id="paymentMethod" class="form-control">
                    <option value="cash">Tunai</option>
                    <option value="transfer">Transfer</option>
                    <option value="other">Lainnya</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">Jumlah Bayar</label>
                <input type="number" id="paymentAmount" class="form-control" 
                       style="font-size: 1.2rem;" oninput="calculateChange()">
            </div>
            
            <div class="form-group">
                <label class="form-label">Kembalian</label>
                <input type="text" id="changeAmount" class="form-control" readonly 
                       style="font-size: 1.2rem; font-weight: bold; color: var(--success-color);">
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button type="button" class="btn" onclick="closePaymentModal()" 
                        style="background-color: #6b7280; color: white;">Batal</button>
                <button type="button" class="btn btn-success" onclick="completeSale()">
                    <i class="fas fa-check"></i> Selesaikan Transaksi
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let cart = [];
        let total = 0;
        
        // Barcode input handler
        document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const barcode = this.value.trim();
                if (barcode) {
                    searchProduct(barcode);
                    this.value = '';
                }
            }
        });
        
        function searchProduct(barcode) {
            fetch('ajax/search_product.php?barcode=' + barcode)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addToCart(data.product);
                    } else {
                        alert('Produk tidak ditemukan!');
                    }
                });
        }
        
        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);
            
        if (existingItem) {
            if (existingItem.quantity < product.stock) {
                existingItem.quantity++;
                // Tambahkan baris ini: Update subtotal
                existingItem.subtotal = existingItem.price * existingItem.quantity;
            } else {
                alert('Stok tidak mencukupi!');
                return;
            }
        } else {
            cart.push({
                id: product.id,
                code: product.code,
                name: product.name,
                price: parseFloat(product.selling_price),
                stock: product.stock,
                quantity: 1,
                // Tambahkan properti subtotal saat item pertama kali ditambahkan
                subtotal: parseFloat(product.selling_price) * 1 
            });
        }

        updateCart();
        
        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCart();
        }
        
        function updateQuantity(index, change) {
            const item = cart[index];
            const newQty = item.quantity + change;
            
            if (newQty > 0 && newQty <= item.stock) {
                item.quantity = newQty;
                updateCart();
            }
        }
        
        function updateCart() {
            const cartItems = document.getElementById('cartItems');
            total = 0;
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<p style="text-align: center; color: #6b7280;">Belum ada produk</p>';
                document.getElementById('payButton').disabled = true;
            } 
            
            else {
                let html = '';
                cart.forEach((item, index) => {
                    const subtotal = item.price * item.quantity;
                    total += subtotal;
                    
                    html += `
                        <div class="cart-item">
                            <div>
                                <strong>${item.name}</strong><br>
                                <small>${item.code} - ${formatRupiah(item.price)}</small>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <button onclick="updateQuantity(${index}, -1)" class="btn btn-sm" 
                                        style="padding: 0.25rem 0.5rem; background: #e5e7eb;">-</button>
                                <span>${item.quantity}</span>
                                <button onclick="updateQuantity(${index}, 1)" class="btn btn-sm" 
                                        style="padding: 0.25rem 0.5rem; background: #e5e7eb;">+</button>
                                <button onclick="removeFromCart(${index})" class="btn btn-sm btn-danger" 
                                        style="padding: 0.25rem 0.5rem;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                cartItems.innerHTML = html;
                document.getElementById('payButton').disabled = false;
            }
            
            document.getElementById('totalAmount').textContent = formatRupiah(total);
        }
        
        function formatRupiah(amount) {
            return 'Rp ' + amount.toLocaleString('id-ID');
        }
        
        function processPayment() {
            if (cart.length === 0) return;
            
            document.getElementById('modalTotal').value = formatRupiah(total);
            document.getElementById('paymentAmount').value = total;
            document.getElementById('changeAmount').value = formatRupiah(0);
            document.getElementById('paymentModal').style.display = 'block';
        }
        
        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
        }
        
        function calculateChange() {
            const paid = parseFloat(document.getElementById('paymentAmount').value) || 0;
            const change = paid - total;
            document.getElementById('changeAmount').value = formatRupiah(Math.max(0, change));
        }
        
        function completeSale() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
            
            if (paymentAmount < total) {
                alert('Jumlah pembayaran kurang!');
                return;
            }
            
            // Prepare sale data
            const saleData = {
                items: cart,
                total: total,
                payment_method: paymentMethod,
                payment_received: paymentAmount,
                change_amount: paymentAmount - total
            };
            
            // Send to server
            fetch('ajax/process_sale.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(saleData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Transaksi berhasil! No. Invoice: ' + data.invoice_no);
                    
                    // Print receipt if needed
                    if (confirm('Cetak struk?')) {
                        window.open('print_receipt.php?id=' + data.sale_id, '_blank');
                    }
                    
                    // Clear cart
                    cart = [];
                    updateCart();
                    closePaymentModal();
                } else {
                    alert('Error: ' + data.message);
                }
            });
        }
        
        // Window click handler
        window.onclick = function(event) {
            if (event.target.className === 'modal') {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
<?php $conn->close(); ?>