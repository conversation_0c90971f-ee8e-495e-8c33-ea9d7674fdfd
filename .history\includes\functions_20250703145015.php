<?php
// config/functions.php - Helper Functions

/**
 * Database connection function
 */
function connectDB() {
    // Database configuration - adjust these values according to your setup
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'k5n_inventory';
    
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }
    
    // Set charset to UTF-8
    $conn->set_charset("utf8");
    
    return $conn;
}

/**
 * Escape string for SQL queries
 */
function escape($conn, $string) {
    return mysqli_real_escape_string($conn, trim($string));
}

/**
 * Format Indonesian Rupiah currency
 */
function formatRupiah($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

/**
 * Check if user is logged in
 */
function checkLogin() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
}

/**
 * Check if user is admin
 */
function checkAdmin() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
        header('Location: login.php');
        exit();
    }
}

/**
 * Generate product code based on category
 */
function generateProductCode($conn, $category_name) {
    // Get first 3 letters of category name
    $prefix = strtoupper(substr($category_name, 0, 3));
    
    // Get last product code for this category
    $result = $conn->query("SELECT code FROM products WHERE code LIKE '$prefix%' ORDER BY code DESC LIMIT 1");
    
    if ($result && $result->num_rows > 0) {
        $last_code = $result->fetch_assoc()['code'];
        $last_number = intval(substr($last_code, 3));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return $prefix . str_pad($new_number, 5, '0', STR_PAD_LEFT);
}

/**
 * Generate invoice number
 */
function generateInvoiceNo($conn) {
    $today = date('Ymd');
    
    // Get last invoice number for today
    $result = $conn->query("SELECT invoice_no FROM sales WHERE DATE(created_at) = CURDATE() ORDER BY id DESC LIMIT 1");
    
    if ($result && $result->num_rows > 0) {
        $last_invoice = $result->fetch_assoc()['invoice_no'];
        $last_number = intval(substr($last_invoice, -4));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return 'INV' . $today . str_pad($new_number, 4, '0', STR_PAD_LEFT);
}

/**
 * Get user information
 */
function getUserInfo($conn, $user_id) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_assoc();
}

/**
 * Log activity
 */
function logActivity($conn, $user_id, $action, $description = '') {
    $stmt = $conn->prepare("INSERT INTO activity_log (user_id, action, description) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $user_id, $action, $description);
    return $stmt->execute();
}

/**
 * Get system settings
 */
function getSetting($conn, $key, $default = null) {
    $stmt = $conn->prepare("SELECT value FROM settings WHERE key = ?");
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['value'];
    }
    
    return $default;
}

/**
 * Set system setting
 */
function setSetting($conn, $key, $value) {
    $stmt = $conn->prepare("INSERT INTO settings (key, value) VALUES (?, ?) ON DUPLICATE KEY UPDATE value = ?");
    $stmt->bind_param("sss", $key, $value, $value);
    return $stmt->execute();
}

/**
 * Calculate profit
 */
function calculateProfit($selling_price, $purchase_price, $quantity = 1) {
    return ($selling_price - $purchase_price) * $quantity;
}

/**
 * Get low stock products
 */
function getLowStockProducts($conn, $limit = 10) {
    $query = "SELECT * FROM products WHERE stock <= min_stock ORDER BY stock ASC LIMIT ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    return $stmt->get_result();
}

/**
 * Format date to Indonesian format
 */
function formatTanggal($date) {
    $bulan = [
        1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $bulan[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Validate email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Get sales report data
 */
function getSalesReport($conn, $start_date, $end_date) {
    $query = "
        SELECT 
            DATE(s.created_at) as sale_date,
            COUNT(s.id) as total_transactions,
            SUM(s.total_amount) as total_sales,
            SUM(si.quantity) as total_items
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        WHERE DATE(s.created_at) BETWEEN ? AND ?
        GROUP BY DATE(s.created_at)
        ORDER BY sale_date ASC
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    return $stmt->get_result();
}

/**
 * Clean old logs (optional, for maintenance)
 */
function cleanOldLogs($conn, $days = 30) {
    $query = "DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $days);
    return $stmt->execute();
}
?>