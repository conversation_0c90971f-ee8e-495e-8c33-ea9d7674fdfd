<?php
// ajax/search_product.php - Search by barcode/code for Enhanced Database
// Create this file in ajax/ folder

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../config/database.php';
checkLogin();

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$barcode = $_GET['barcode'] ?? '';

if (empty($barcode)) {
    echo json_encode(['success' => false, 'message' => 'Barcode tidak boleh kosong']);
    exit;
}

try {
    $conn = connectDB();
    
    // Enhanced search with category and supplier info
    $stmt = $conn->prepare("
        SELECT 
            p.*,
            c.name as category_name,
            s.name as supplier_name,
            CASE 
                WHEN p.stock <= 0 THEN 'out_of_stock'
                WHEN p.stock <= p.min_stock THEN 'low_stock'
                ELSE 'normal'
            END as stock_status
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE (p.barcode = ? OR p.code = ? OR p.name LIKE ?) 
        AND p.is_active = TRUE 
        AND p.stock > 0
        ORDER BY 
            CASE 
                WHEN p.barcode = ? THEN 1
                WHEN p.code = ? THEN 2
                ELSE 3
            END,
            p.stock DESC
        LIMIT 1
    ");
    
    $search_term = "%$barcode%";
    $stmt->bind_param("ssssss", $barcode, $barcode, $search_term, $barcode, $barcode);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        // Add computed fields for frontend compatibility
        $row['price_formatted'] = formatRupiah($row['selling_price']);
        $row['purchase_price_formatted'] = formatRupiah($row['purchase_price']);
        $row['profit_margin'] = $row['purchase_price'] > 0 ? 
            round((($row['selling_price'] - $row['purchase_price']) / $row['selling_price']) * 100, 2) : 0;
        
        // Parse gallery JSON if exists
        if (!empty($row['gallery'])) {
            $row['gallery'] = json_decode($row['gallery'], true);
        }
        
        echo json_encode([
            'success' => true,
            'product' => $row,
            'timestamp' => date('c')
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Produk tidak ditemukan atau stok habis',
            'search_term' => $barcode,
            'timestamp' => date('c')
        ]);
    }
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    error_log("Barcode search error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false, 
        'message' => 'Terjadi kesalahan sistem',
        'error_code' => 'SEARCH_ERROR',
        'timestamp' => date('c')
    ]);
}
?>