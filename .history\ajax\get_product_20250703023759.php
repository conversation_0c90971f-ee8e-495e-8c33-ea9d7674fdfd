<?php
// ajax/get_product.php (VERSI AMAN)
require_once '../config/database.php';

header('Content-Type: application/json');

$id = (int)($_GET['id'] ?? 0);

if ($id > 0) {
    $conn = connectDB();
    
    // Menggunakan prepared statement untuk mencegah SQL Injection
    $stmt = $conn->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->bind_param("i", $id); // 'i' untuk integer
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $product = $result->fetch_assoc();
        // formatRupiah sudah aman karena memproses angka
        $product['price_formatted'] = formatRupiah($product['selling_price']); 
        echo json_encode($product);
    } else {
        echo json_encode(['error' => 'Product not found']);
    }
    
    $stmt->close();
    $conn->close();
} else {
    echo json_encode(['error' => 'Invalid ID']);
}
?>