-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 02, 2025 at 09:49 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `k5n_inventory`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `created_at`) VALUES
(1, 'Baterai', 'Berbagai jenis baterai', '2025-07-02 16:24:19'),
(2, 'Elektronik', 'Komponen elektronik', '2025-07-02 16:24:19'),
(3, 'Aksesoris', 'Aksesoris elektronik', '2025-07-02 16:24:19');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `purchase_price` decimal(12,2) DEFAULT 0.00,
  `selling_price` decimal(12,2) NOT NULL,
  `stock` int(11) DEFAULT 0,
  `min_stock` int(11) DEFAULT 5,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `code`, `name`, `category_id`, `barcode`, `purchase_price`, `selling_price`, `stock`, `min_stock`, `description`, `created_at`, `updated_at`) VALUES

-- --------------------------------------------------------

--
-- Table structure for table `sales`
--

CREATE TABLE `sales` (
  `id` int(11) NOT NULL,
  `invoice_no` varchar(50) NOT NULL,
  `user_id` int(11) NOT NULL,
  `total_amount` decimal(12,2) NOT NULL,
  `payment_method` enum('cash','transfer','other') DEFAULT 'cash',
  `payment_received` decimal(12,2) DEFAULT 0.00,
  `change_amount` decimal(12,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sales`
--

INSERT INTO `sales` (`id`, `invoice_no`, `user_id`, `total_amount`, `payment_method`, `payment_received`, `change_amount`, `notes`, `created_at`) VALUES
(1, 'INV202507020001', 1, 35000.00, 'cash', 35000.00, 0.00, NULL, '2025-07-02 17:03:02'),
(2, 'INV202507020002', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:04'),
(3, 'INV202507020003', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:04'),
(4, 'INV202507020004', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:05'),
(5, 'INV202507020005', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:05'),
(6, 'INV202507020006', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:05'),
(7, 'INV202507020007', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:05'),
(8, 'INV202507020008', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:05'),
(9, 'INV202507020009', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:06'),
(10, 'INV202507020010', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:06'),
(11, 'INV202507020011', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:06'),
(12, 'INV202507020012', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:07'),
(13, 'INV202507020013', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:07'),
(14, 'INV202507020014', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:09'),
(15, 'INV202507020015', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(16, 'INV202507020016', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(17, 'INV202507020017', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(18, 'INV202507020018', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(19, 'INV202507020019', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(20, 'INV202507020020', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:10'),
(21, 'INV202507020021', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(22, 'INV202507020022', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(23, 'INV202507020023', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(24, 'INV202507020024', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(25, 'INV202507020025', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(26, 'INV202507020026', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:11'),
(27, 'INV202507020027', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:12'),
(28, 'INV202507020028', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:16'),
(29, 'INV202507020029', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:16'),
(30, 'INV202507020030', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:31'),
(31, 'INV202507020031', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:32'),
(32, 'INV202507020032', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:03:32'),
(33, 'INV202507020033', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:04:09'),
(34, 'INV202507020034', 1, 35000.00, 'transfer', 35000.00, 0.00, NULL, '2025-07-02 17:04:11'),
(35, 'INV202507020035', 1, 100000.00, 'transfer', 100000.00, 0.00, NULL, '2025-07-02 17:04:17'),
(36, 'INV202507020036', 1, 100000.00, 'other', 100000.00, 0.00, NULL, '2025-07-02 17:04:20'),
(37, 'INV202507020037', 1, 100000.00, 'cash', 100000.00, 0.00, NULL, '2025-07-02 17:04:27'),
(38, 'INV202507020038', 1, 70000.00, 'cash', 70000.00, 0.00, NULL, '2025-07-02 17:07:10'),
(41, 'INV202507020039', 1, 65000.00, 'cash', 65000.00, 0.00, NULL, '2025-07-02 18:27:53');

-- --------------------------------------------------------

--
-- Stand-in structure for view `sales_report`
-- (See below for the actual view)
--
CREATE TABLE `sales_report` (
`id` int(11)
,`invoice_no` varchar(50)
,`sale_date` timestamp
,`cashier` varchar(100)
,`total_amount` decimal(12,2)
,`payment_method` enum('cash','transfer','other')
,`total_items` bigint(21)
,`total_quantity` decimal(32,0)
);

-- --------------------------------------------------------

--
-- Table structure for table `sale_items`
--

CREATE TABLE `sale_items` (
  `id` int(11) NOT NULL,
  `sale_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(12,2) NOT NULL,
  `subtotal` decimal(12,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sale_items`
--

INSERT INTO `sale_items` (`id`, `sale_id`, `product_id`, `quantity`, `price`, `subtotal`) VALUES
(1, 1, 1, 1, 35000.00, 35000.00),
(2, 2, 1, 1, 35000.00, 35000.00),
(3, 3, 1, 1, 35000.00, 35000.00),
(4, 4, 1, 1, 35000.00, 35000.00),
(5, 5, 1, 1, 35000.00, 35000.00),
(6, 6, 1, 1, 35000.00, 35000.00),
(7, 7, 1, 1, 35000.00, 35000.00),
(8, 8, 1, 1, 35000.00, 35000.00),
(9, 9, 1, 1, 35000.00, 35000.00),
(10, 10, 1, 1, 35000.00, 35000.00),
(11, 11, 1, 1, 35000.00, 35000.00),
(12, 12, 1, 1, 35000.00, 35000.00),
(13, 13, 1, 1, 35000.00, 35000.00),
(14, 14, 1, 1, 35000.00, 35000.00),
(15, 15, 1, 1, 35000.00, 35000.00),
(16, 16, 1, 1, 35000.00, 35000.00),
(17, 17, 1, 1, 35000.00, 35000.00),
(18, 18, 1, 1, 35000.00, 35000.00),
(19, 19, 1, 1, 35000.00, 35000.00),
(20, 20, 1, 1, 35000.00, 35000.00),
(21, 21, 1, 1, 35000.00, 35000.00),
(22, 22, 1, 1, 35000.00, 35000.00),
(23, 23, 1, 1, 35000.00, 35000.00),
(24, 24, 1, 1, 35000.00, 35000.00),
(25, 25, 1, 1, 35000.00, 35000.00),
(26, 26, 1, 1, 35000.00, 35000.00),
(27, 27, 1, 1, 35000.00, 35000.00),
(28, 28, 1, 1, 35000.00, 35000.00),
(29, 29, 1, 1, 35000.00, 35000.00),
(30, 30, 1, 1, 35000.00, 35000.00),
(31, 31, 1, 1, 35000.00, 35000.00),
(32, 32, 1, 1, 35000.00, 35000.00),
(33, 33, 1, 1, 35000.00, 35000.00),
(34, 34, 1, 1, 35000.00, 35000.00),
(35, 35, 1, 2, 35000.00, 70000.00),
(36, 35, 2, 1, 30000.00, 30000.00),
(37, 36, 1, 2, 35000.00, 70000.00),
(38, 36, 2, 1, 30000.00, 30000.00),
(39, 37, 2, 1, 30000.00, 30000.00),
(40, 37, 1, 2, 35000.00, 70000.00),
(41, 38, 1, 2, 35000.00, 70000.00),
(45, 41, 2, 1, 30000.00, 30000.00),
(46, 41, 1, 1, 35000.00, 35000.00);

--
-- Triggers `sale_items`
--
DELIMITER $$
CREATE TRIGGER `after_sale_insert` AFTER INSERT ON `sale_items` FOR EACH ROW BEGIN
    -- Kurangi stock
    UPDATE products 
    SET stock = stock - NEW.quantity 
    WHERE id = NEW.product_id;
    
    -- Catat history
    INSERT INTO stock_history (product_id, type, quantity, reference_no, user_id)
    SELECT NEW.product_id, 'out', NEW.quantity, s.invoice_no, s.user_id
    FROM sales s WHERE s.id = NEW.sale_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `stock_history`
--

CREATE TABLE `stock_history` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `type` enum('in','out','adjustment') NOT NULL,
  `quantity` int(11) NOT NULL,
  `reference_no` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `stock_history`
--

INSERT INTO `stock_history` (`id`, `product_id`, `type`, `quantity`, `reference_no`, `notes`, `user_id`, `created_at`) VALUES
(1, 1, 'out', 1, 'INV202507020001', NULL, 1, '2025-07-02 17:03:02'),
(2, 1, 'out', 1, 'INV202507020002', NULL, 1, '2025-07-02 17:03:04'),
(3, 1, 'out', 1, 'INV202507020003', NULL, 1, '2025-07-02 17:03:04'),
(4, 1, 'out', 1, 'INV202507020004', NULL, 1, '2025-07-02 17:03:05'),
(5, 1, 'out', 1, 'INV202507020005', NULL, 1, '2025-07-02 17:03:05'),
(6, 1, 'out', 1, 'INV202507020006', NULL, 1, '2025-07-02 17:03:05'),
(7, 1, 'out', 1, 'INV202507020007', NULL, 1, '2025-07-02 17:03:05'),
(8, 1, 'out', 1, 'INV202507020008', NULL, 1, '2025-07-02 17:03:05'),
(9, 1, 'out', 1, 'INV202507020009', NULL, 1, '2025-07-02 17:03:06'),
(10, 1, 'out', 1, 'INV202507020010', NULL, 1, '2025-07-02 17:03:06'),
(11, 1, 'out', 1, 'INV202507020011', NULL, 1, '2025-07-02 17:03:06'),
(12, 1, 'out', 1, 'INV202507020012', NULL, 1, '2025-07-02 17:03:07'),
(13, 1, 'out', 1, 'INV202507020013', NULL, 1, '2025-07-02 17:03:07'),
(14, 1, 'out', 1, 'INV202507020014', NULL, 1, '2025-07-02 17:03:09'),
(15, 1, 'out', 1, 'INV202507020015', NULL, 1, '2025-07-02 17:03:10'),
(16, 1, 'out', 1, 'INV202507020016', NULL, 1, '2025-07-02 17:03:10'),
(17, 1, 'out', 1, 'INV202507020017', NULL, 1, '2025-07-02 17:03:10'),
(18, 1, 'out', 1, 'INV202507020018', NULL, 1, '2025-07-02 17:03:10'),
(19, 1, 'out', 1, 'INV202507020019', NULL, 1, '2025-07-02 17:03:10'),
(20, 1, 'out', 1, 'INV202507020020', NULL, 1, '2025-07-02 17:03:10'),
(21, 1, 'out', 1, 'INV202507020021', NULL, 1, '2025-07-02 17:03:11'),
(22, 1, 'out', 1, 'INV202507020022', NULL, 1, '2025-07-02 17:03:11'),
(23, 1, 'out', 1, 'INV202507020023', NULL, 1, '2025-07-02 17:03:11'),
(24, 1, 'out', 1, 'INV202507020024', NULL, 1, '2025-07-02 17:03:11'),
(25, 1, 'out', 1, 'INV202507020025', NULL, 1, '2025-07-02 17:03:11'),
(26, 1, 'out', 1, 'INV202507020026', NULL, 1, '2025-07-02 17:03:11'),
(27, 1, 'out', 1, 'INV202507020027', NULL, 1, '2025-07-02 17:03:12'),
(28, 1, 'out', 1, 'INV202507020028', NULL, 1, '2025-07-02 17:03:16'),
(29, 1, 'out', 1, 'INV202507020029', NULL, 1, '2025-07-02 17:03:16'),
(30, 1, 'out', 1, 'INV202507020030', NULL, 1, '2025-07-02 17:03:31'),
(31, 1, 'out', 1, 'INV202507020031', NULL, 1, '2025-07-02 17:03:32'),
(32, 1, 'out', 1, 'INV202507020032', NULL, 1, '2025-07-02 17:03:32'),
(33, 1, 'out', 1, 'INV202507020033', NULL, 1, '2025-07-02 17:04:09'),
(34, 1, 'out', 1, 'INV202507020034', NULL, 1, '2025-07-02 17:04:11'),
(35, 1, 'out', 2, 'INV202507020035', NULL, 1, '2025-07-02 17:04:17'),
(36, 2, 'out', 1, 'INV202507020035', NULL, 1, '2025-07-02 17:04:17'),
(37, 1, 'out', 2, 'INV202507020036', NULL, 1, '2025-07-02 17:04:20'),
(38, 2, 'out', 1, 'INV202507020036', NULL, 1, '2025-07-02 17:04:20'),
(39, 2, 'out', 1, 'INV202507020037', NULL, 1, '2025-07-02 17:04:27'),
(40, 1, 'out', 2, 'INV202507020037', NULL, 1, '2025-07-02 17:04:27'),
(41, 1, 'out', 2, 'INV202507020038', NULL, 1, '2025-07-02 17:07:10'),
(45, 2, 'out', 1, 'INV202507020039', NULL, 1, '2025-07-02 18:27:53'),
(46, 1, 'out', 1, 'INV202507020039', NULL, 1, '2025-07-02 18:27:53');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','user') DEFAULT 'user',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `role`, `created_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin', '2025-07-02 16:24:19'),
(2, 'user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kasir 1', 'user', '2025-07-02 16:24:19'),
(3, 'user2', '$2y$10$rqyzPNB6fZrbPToEYHH2leymjdLO18vr7pDjPC4U.EcMeweHlBJTW', 'Kasir2', 'user', '2025-07-02 17:02:25');

-- --------------------------------------------------------

--
-- Structure for view `sales_report`
--
DROP TABLE IF EXISTS `sales_report`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `sales_report`  AS SELECT `s`.`id` AS `id`, `s`.`invoice_no` AS `invoice_no`, `s`.`created_at` AS `sale_date`, `u`.`full_name` AS `cashier`, `s`.`total_amount` AS `total_amount`, `s`.`payment_method` AS `payment_method`, count(`si`.`id`) AS `total_items`, sum(`si`.`quantity`) AS `total_quantity` FROM ((`sales` `s` join `users` `u` on(`s`.`user_id` = `u`.`id`)) left join `sale_items` `si` on(`s`.`id` = `si`.`sale_id`)) GROUP BY `s`.`id` ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD UNIQUE KEY `barcode` (`barcode`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `sales`
--
ALTER TABLE `sales`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invoice_no` (`invoice_no`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `sale_items`
--
ALTER TABLE `sale_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sale_id` (`sale_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `stock_history`
--
ALTER TABLE `stock_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `sales`
--
ALTER TABLE `sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `sale_items`
--
ALTER TABLE `sale_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `stock_history`
--
ALTER TABLE `stock_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `sales`
--
ALTER TABLE `sales`
  ADD CONSTRAINT `sales_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `sale_items`
--
ALTER TABLE `sale_items`
  ADD CONSTRAINT `sale_items_ibfk_1` FOREIGN KEY (`sale_id`) REFERENCES `sales` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `sale_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`);

--
-- Constraints for table `stock_history`
--
ALTER TABLE `stock_history`
  ADD CONSTRAINT `stock_history_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  ADD CONSTRAINT `stock_history_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
