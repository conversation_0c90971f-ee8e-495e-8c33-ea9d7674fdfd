<?php
// test_existing_structure.php - Test struktur yang sudah ada
session_start();

// Simulate logged in user for testing
$_SESSION['user_id'] = 1;
$_SESSION['role'] = 'admin';

echo "<h2>K5N System Testing - Existing Structure</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    require_once 'config/database.php';
    $conn = connectDB();
    echo "✅ Database connection: SUCCESS<br>";
    
    // Test basic query
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Users table accessible: $count users found<br>";
    } else {
        echo "❌ Error querying users table: " . $conn->error . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection: FAILED - " . $e->getMessage() . "<br>";
}

// Test 2: Functions Test
echo "<h3>2. Functions Test</h3>";
try {
    // Test formatRupiah
    $formatted = formatRupiah(35000);
    echo "✅ formatRupiah(35000): $formatted<br>";
    
    // Test generateInvoiceNo
    if (function_exists('generateInvoiceNo')) {
        $invoice = generateInvoiceNo($conn);
        echo "✅ generateInvoiceNo(): $invoice<br>";
    } else {
        echo "❌ generateInvoiceNo function not found<br>";
    }
    
    // Test escape function
    if (function_exists('escape')) {
        $escaped = escape($conn, "test'string");
        echo "✅ escape function: works<br>";
    } else {
        echo "❌ escape function not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Functions test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 3: Include Functions File
echo "<h3>3. Include Test</h3>";
try {
    if (file_exists('includes/functions.php')) {
        require_once 'includes/functions.php';
        echo "✅ includes/functions.php: LOADED<br>";
        
        // Test if getSalesSummary exists (from your existing functions)
        if (function_exists('getSalesSummary')) {
            echo "✅ getSalesSummary function: EXISTS<br>";
        } else {
            echo "❌ getSalesSummary function: NOT FOUND<br>";
        }
        
    } else {
        echo "❌ includes/functions.php: FILE NOT FOUND<br>";
    }
} catch (Exception $e) {
    echo "❌ Include test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 4: Check Database Tables
echo "<h3>4. Database Tables Check</h3>";
try {
    $tables = ['users', 'products', 'categories', 'sales', 'sale_items', 'stock_history'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "✅ Table '$table': EXISTS ($count records)<br>";
        } else {
            echo "❌ Table '$table': NOT FOUND<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Table check: FAILED - " . $e->getMessage() . "<br>";
}

// Test 5: Products with Stock
echo "<h3>5. Products Test</h3>";
try {
    $products_result = $conn->query("SELECT * FROM products WHERE stock > 0 LIMIT 3");
    if ($products_result->num_rows > 0) {
        echo "✅ Products with stock found:<br>";
        while ($product = $products_result->fetch_assoc()) {
            echo "&nbsp;&nbsp;- {$product['name']} (Code: {$product['code']}, Stock: {$product['stock']})<br>";
        }
    } else {
        echo "❌ No products with stock found<br>";
    }
} catch (Exception $e) {
    echo "❌ Products test: FAILED - " . $e->getMessage() . "<br>";
}

// Test 6: AJAX Endpoint Test
echo "<h3>6. AJAX Endpoint Test</h3>";
echo "<button onclick='testProcessSale()' style='padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;'>Test Process Sale</button>";
echo "<div id='ajaxResult' style='margin-top: 10px; padding: 10px; background: #f0f0f0; display: none;'></div>";

// Close connection
if (isset($conn)) {
    $conn->close();
}
?>

<script>
function testProcessSale() {
    // Show result area
    document.getElementById('ajaxResult').style.display = 'block';
    document.getElementById('ajaxResult').innerHTML = 'Testing...';
    
    const testData = {
        items: [
            {
                id: 1,
                name: 'Test Product',
                code: 'BAT26650', 
                price: 35000,
                quantity: 1
            }
        ],
        total: 35000,
        payment_method: 'cash',
        payment_received: 35000,
        change_amount: 0
    };
    
    console.log('Testing with data:', testData);
    
    fetch('ajax/process_sale.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            console.log('Parsed JSON:', data);
            
            if (data.success) {
                document.getElementById('ajaxResult').innerHTML = 
                    '<strong>✅ SUCCESS!</strong><br>' +
                    'Invoice: ' + data.invoice_no + '<br>' +
                    'Sale ID: ' + data.sale_id + '<br>' +
                    'Message: ' + data.message;
            } else {
                document.getElementById('ajaxResult').innerHTML = 
                    '<strong>❌ ERROR:</strong><br>' + data.message;
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            document.getElementById('ajaxResult').innerHTML = 
                '<strong>❌ JSON Parse Error:</strong><br>' +
                'Raw response: <pre>' + text.substring(0, 500) + '</pre>';
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        document.getElementById('ajaxResult').innerHTML = 
            '<strong>❌ Network Error:</strong><br>' + error.message;
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; }
h3 { color: #666; margin-top: 20px; }
button:hover { background: #005a8b !important; }
pre { background: #f5f5f5; padding: 10px; border-left: 3px solid #007cba; }
</style>