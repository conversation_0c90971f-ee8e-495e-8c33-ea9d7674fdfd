<?php
// ajax/process_sale.php - Final Compatible Version
// Prevent any HTML output before JSON
ob_start();

// Clean any previous output
if (ob_get_level()) {
    ob_clean();
}

// Set JSON header first
header('Content-Type: application/json');

// Disable HTML error output
ini_set('display_errors', 0);
ini_set('log_errors', 1);

try {
    // Start session if not started
    if (session_status() == PHP_SESSION_NONE) {
if (session_status() == PHP_SESSION_NONE) {
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
}
    }
    
    // Include required files using the existing structure
    if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
    require_once '../config/database.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not logged in. Please login first.');
    }
    
    // Get and validate input
    $input = file_get_contents('php://input');
    if (empty($input)) {
        throw new Exception('No input data received');
    }
    
    $data = json_decode($input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }
    
    // Validate required fields
    if (empty($data['items'])) {
        throw new Exception('No items in cart');
    }
    
    if (!isset($data['total']) || $data['total'] <= 0) {
        throw new Exception('Invalid total amount');
    }
    
    // Connect to database
    $conn = connectDB();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Extract and validate data
    $user_id = intval($_SESSION['user_id']);
    $items = $data['items'];
    $total = floatval($data['total']);
    $payment_method = isset($data['payment_method']) ? escape($conn, $data['payment_method']) : 'cash';
    $payment_received = floatval($data['payment_received'] ?? 0);
    $change_amount = floatval($data['change_amount'] ?? 0);
    
    // Validate payment
    if ($payment_received < $total) {
        throw new Exception('Payment amount is insufficient');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    // Generate invoice number using existing function
    $invoice_no = generateInvoiceNo($conn);
    
    // Insert sale record
    $stmt = $conn->prepare("INSERT INTO sales (invoice_no, user_id, total_amount, payment_method, payment_received, change_amount) VALUES (?, ?, ?, ?, ?, ?)");
    
    if (!$stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }
    
    $stmt->bind_param("sidsdd", $invoice_no, $user_id, $total, $payment_method, $payment_received, $change_amount);
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert sale: ' . $stmt->error);
    }
    
    $sale_id = $conn->insert_id;
    $stmt->close();
    
    // Insert sale items and validate stock
    $calculated_total = 0;
    foreach ($items as $item) {
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        $price = floatval($item['price']);
        $subtotal = $price * $quantity;
        $calculated_total += $subtotal;
        
        // Validate item data
        if ($quantity <= 0) {
            throw new Exception('Invalid quantity for product ID: ' . $product_id);
        }
        
        // Check stock availability
        $stock_check = $conn->prepare("SELECT stock, name FROM products WHERE id = ?");
        $stock_check->bind_param("i", $product_id);
        $stock_check->execute();
        $result = $stock_check->get_result();
        $product = $result->fetch_assoc();
        
        if (!$product) {
            throw new Exception('Product not found: ID ' . $product_id);
        }
        
        if ($product['stock'] < $quantity) {
            throw new Exception('Insufficient stock for ' . $product['name'] . '. Available: ' . $product['stock'] . ', Required: ' . $quantity);
        }
        
        // Insert sale item
        $item_stmt = $conn->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) VALUES (?, ?, ?, ?, ?)");
        $item_stmt->bind_param("iiidd", $sale_id, $product_id, $quantity, $price, $subtotal);
        
        if (!$item_stmt->execute()) {
            throw new Exception('Failed to insert sale item: ' . $item_stmt->error);
        }
        
        $item_stmt->close();
        $stock_check->close();
    }
    
    // Verify calculated total matches sent total
    if (abs($calculated_total - $total) > 0.01) {
        throw new Exception('Total mismatch. Calculated: ' . $calculated_total . ', Sent: ' . $total);
    }
    
    // Commit transaction
    $conn->commit();
    
    // Success response
    $response = [
        'success' => true,
        'sale_id' => $sale_id,
        'invoice_no' => $invoice_no,
        'message' => 'Transaction completed successfully',
        'total' => $total,
        'items_count' => count($items)
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback on error
    if (isset($conn) && $conn) {
        $conn->rollback();
    }
    
    // Error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    
} finally {
    // Clean up
    if (isset($conn) && $conn) {
        $conn->close();
    }
}
?>