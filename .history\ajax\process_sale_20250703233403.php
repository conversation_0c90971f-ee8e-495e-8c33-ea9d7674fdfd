<?php
// ajax/process_sale.php - Enhanced Database Compatible Version
// Create this file in ajax/ folder

// Prevent any HTML output before JSON
ob_start();

// Clean any previous output
if (ob_get_level()) {
    ob_clean();
}

// Set JSON header first
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Disable HTML error output
ini_set('display_errors', 0);
ini_set('log_errors', 1);

try {
    // Start session if not started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // Include required files
    require_once '../config/database.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not logged in. Please login first.');
    }
    
    // Get and validate input
    $input = file_get_contents('php://input');
    if (empty($input)) {
        throw new Exception('No input data received');
    }
    
    $data = json_decode($input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }
    
    // Validate required fields
    if (empty($data['items'])) {
        throw new Exception('No items in cart');
    }
    
    if (!isset($data['total_amount']) || $data['total_amount'] <= 0) {
        // Check if 'total' exists (backward compatibility)
        if (isset($data['total']) && $data['total'] > 0) {
            $data['total_amount'] = $data['total'];
        } else {
            throw new Exception('Invalid total amount');
        }
    }
    
    // Connect to database
    $conn = connectDB();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }
    
    // Extract and validate data with enhanced structure
    $user_id = intval($_SESSION['user_id']);
    $items = $data['items'];
    $subtotal = floatval($data['subtotal'] ?? $data['total_amount']);
    $total_amount = floatval($data['total_amount']);
    $tax_amount = floatval($data['tax_amount'] ?? 0);
    $discount_amount = floatval($data['discount_amount'] ?? 0);
    
    // Payment details
    $payment_method = isset($data['payment_method']) ? escape($conn, $data['payment_method']) : 'cash';
    $payment_received = floatval($data['payment_received'] ?? 0);
    $change_amount = floatval($data['change_amount'] ?? 0);
    $payment_reference = isset($data['payment_reference']) ? escape($conn, $data['payment_reference']) : null;
    
    // Customer information (optional)
    $customer_name = isset($data['customer_name']) && !empty(trim($data['customer_name'])) ? 
        escape($conn, trim($data['customer_name'])) : null;
    $customer_phone = isset($data['customer_phone']) && !empty(trim($data['customer_phone'])) ? 
        escape($conn, trim($data['customer_phone'])) : null;
    $customer_email = isset($data['customer_email']) && !empty(trim($data['customer_email'])) ? 
        escape($conn, trim($data['customer_email'])) : null;
    
    // Additional information
    $notes = isset($data['notes']) && !empty(trim($data['notes'])) ? 
        escape($conn, trim($data['notes'])) : null;
    $internal_notes = isset($data['internal_notes']) && !empty(trim($data['internal_notes'])) ? 
        escape($conn, trim($data['internal_notes'])) : null;
    
    // Validate payment
    if ($payment_received < $total_amount) {
        throw new Exception('Payment amount is insufficient');
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    // Generate invoice number using enhanced function
    $invoice_no = generateInvoiceNo($conn);
    
    // Insert sale record with enhanced structure
    $stmt = $conn->prepare("
        INSERT INTO sales (
            invoice_no, user_id, customer_name, customer_phone, customer_email,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, payment_received, change_amount, payment_reference,
            notes, internal_notes, status, sale_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'paid', ?, ?, ?, ?, ?, 'completed', CURDATE())
    ");
    
    if (!$stmt) {
        throw new Exception('Prepare statement failed: ' . $conn->error);
    }
    
    $stmt->bind_param("sisssdddssddssss", 
        $invoice_no, $user_id, $customer_name, $customer_phone, $customer_email,
        $subtotal, $tax_amount, $discount_amount, $total_amount,
        $payment_method, $payment_received, $change_amount, $payment_reference,
        $notes, $internal_notes
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert sale: ' . $stmt->error);
    }
    
    $sale_id = $conn->insert_id;
    $stmt->close();
    
    // Insert sale items and validate stock with enhanced structure
    $calculated_total = 0;
    $items_processed = 0;
    
    foreach ($items as $item) {
        $product_id = intval($item['id']);
        $quantity = intval($item['quantity']);
        $unit_price = floatval($item['price']); // Using 'price' from frontend for compatibility
        $discount_amount_item = floatval($item['discount_amount'] ?? 0);
        $subtotal_item = ($unit_price * $quantity) - $discount_amount_item;
        $calculated_total += $subtotal_item;
        
        // Validate item data
        if ($quantity <= 0) {
            throw new Exception('Invalid quantity for product ID: ' . $product_id);
        }
        
        if ($unit_price < 0) {
            throw new Exception('Invalid price for product ID: ' . $product_id);
        }
        
        // Get product information and check stock
        $stock_check = $conn->prepare("
            SELECT stock, name, code, selling_price, is_active 
            FROM products 
            WHERE id = ? AND is_active = TRUE
        ");
        $stock_check->bind_param("i", $product_id);
        $stock_check->execute();
        $result = $stock_check->get_result();
        $product = $result->fetch_assoc();
        
        if (!$product) {
            throw new Exception('Product not found or inactive: ID ' . $product_id);
        }
        
        if ($product['stock'] < $quantity) {
            throw new Exception('Insufficient stock for ' . $product['name'] . '. Available: ' . $product['stock'] . ', Required: ' . $quantity);
        }
        
        // Verify price consistency (optional security check)
        if (abs($unit_price - $product['selling_price']) > 0.01) {
            // Log price discrepancy but don't fail transaction
            error_log("Price discrepancy for product {$product_id}: Expected {$product['selling_price']}, Got {$unit_price}");
        }
        
        // Insert sale item with enhanced structure
        $item_stmt = $conn->prepare("
            INSERT INTO sale_items (
                sale_id, product_id, product_name, product_code,
                quantity, unit_price, discount_amount, subtotal, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $item_notes = isset($item['notes']) ? escape($conn, $item['notes']) : null;
        
        $item_stmt->bind_param("iissiddds", 
            $sale_id, $product_id, $product['name'], $product['code'],
            $quantity, $unit_price, $discount_amount_item, $subtotal_item, $item_notes
        );
        
        if (!$item_stmt->execute()) {
            throw new Exception('Failed to insert sale item: ' . $item_stmt->error);
        }
        
        $item_stmt->close();
        $stock_check->close();
        $items_processed++;
    }
    
    // Verify calculated total matches sent total (with small tolerance for floating point)
    if (abs($calculated_total - $subtotal) > 0.01) {
        error_log("Total mismatch: Calculated: $calculated_total, Sent: $subtotal");
        // Don't fail transaction for small discrepancies, but log it
    }
    
    // Commit transaction
    $conn->commit();
    
    // Log successful transaction
    if (function_exists('logActivity')) {
        logActivity("Sale completed: $invoice_no", [
            'sale_id' => $sale_id,
            'total_amount' => $total_amount,
            'items_count' => $items_processed,
            'payment_method' => $payment_method
        ], 'INFO');
    }
    
    // Success response with enhanced data
    $response = [
        'success' => true,
        'sale_id' => $sale_id,
        'invoice_no' => $invoice_no,
        'message' => 'Transaction completed successfully',
        'total' => $total_amount,
        'subtotal' => $subtotal,
        'tax_amount' => $tax_amount,
        'discount_amount' => $discount_amount,
        'items_count' => $items_processed,
        'payment_method' => $payment_method,
        'payment_received' => $payment_received,
        'change_amount' => $change_amount,
        'customer_name' => $customer_name,
        'timestamp' => date('c')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback on error
    if (isset($conn) && $conn) {
        $conn->rollback();
    }
    
    // Log error
    error_log("Sale processing error: " . $e->getMessage());
    
    // Log failed transaction
    if (function_exists('logActivity')) {
        logActivity("Sale failed: " . $e->getMessage(), [
            'user_id' => $_SESSION['user_id'] ?? null,
            'error' => $e->getMessage()
        ], 'ERROR');
    }
    
    // Error response
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'SALE_PROCESSING_ERROR',
        'timestamp' => date('c')
    ];
    
    echo json_encode($response);
    
} finally {
    // Clean up
    if (isset($conn) && $conn) {
        $conn->close();
    }
    
    // Ensure output buffer is clean
    if (ob_get_level()) {
        ob_end_flush();
    }
}
?>